@echo off
echo ========================================
echo Twitter邮箱提取自动化脚本
echo ========================================
echo.

REM 检查必要文件
if not exist "accounts.txt" (
    echo ❌ 错误: 找不到 accounts.txt 文件
    echo 请确保账户文件存在并格式正确
    pause
    exit /b 1
)

if not exist "twitter.txt" (
    echo ❌ 错误: 找不到 twitter.txt 文件
    echo 请确保Twitter token文件存在
    pause
    exit /b 1
)

echo ✅ 文件检查完成
echo.

REM 显示当前状态
echo 📊 当前状态:
python -c "
import os
from auto_profile_setup import read_data_file
from twitter_email_extractor import read_twitter_tokens
try:
    accounts_data = read_data_file('accounts.txt')
    tokens = read_twitter_tokens('twitter.txt')
    print(f'📋 账户文件: accounts.txt')
    print(f'   - 总账户数: {len(accounts_data)}')
    print(f'   - 文件大小: {os.path.getsize(\"accounts.txt\")} 字节')
    print(f'🎯 Token文件: twitter.txt')
    print(f'   - 可用token: {len(tokens)}个')
    print(f'   - 文件大小: {os.path.getsize(\"twitter.txt\")} 字节')
    print('📧 目标页面: https://x.com/settings/email')
    print('💡 注意: 浏览器将保持打开状态供手动操作')
except Exception as e:
    print(f'❌ 读取文件失败: {str(e)}')
"
echo.

REM 询问用户是否继续
set /p choice="是否开始执行Twitter邮箱提取脚本? (y/n): "
if /i "%choice%" neq "y" (
    echo 操作已取消
    pause
    exit /b 0
)

echo.
echo 🚀 启动Twitter邮箱提取脚本...
echo 💡 提示: 脚本会自动访问邮箱设置页面并提取邮箱信息
echo 💡 如果需要登录，会自动使用新的token
echo 💡 浏览器将保持打开状态供您手动输入密码
echo.

REM 运行主脚本
python examples/twitter_email_extractor.py

echo.
echo 📊 执行完成，显示最终状态:
python -c "
import os
try:
    if os.path.exists('email_extraction.log'):
        with open('email_extraction.log', 'r', encoding='utf-8') as f:
            lines = f.readlines()
            print(f'📝 提取日志: email_extraction.log ({len(lines)} 条记录)')
            if lines:
                print('   最近的记录:')
                for line in lines[-3:]:
                    print(f'   {line.strip()}')
    else:
        print('📝 没有生成提取日志')
        
    if os.path.exists('failed_email_extraction.txt'):
        with open('failed_email_extraction.txt', 'r', encoding='utf-8') as f:
            failed_lines = f.readlines()
            print(f'❌ 失败记录: failed_email_extraction.txt ({len(failed_lines)} 条记录)')
    else:
        print('✅ 没有失败记录')
        
    from twitter_email_extractor import read_twitter_tokens
    remaining_tokens = read_twitter_tokens('twitter.txt')
    print(f'🎯 剩余可用token: {len(remaining_tokens)}个')
    
    print('💡 成功打开的浏览器仍在运行，请手动完成密码输入')
except Exception as e:
    print(f'❌ 读取日志文件失败: {str(e)}')
"

pause
