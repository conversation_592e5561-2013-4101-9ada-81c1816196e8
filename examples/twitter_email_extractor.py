import json
import os
import sys
import time
import shutil
import re
import imaplib
import email.message
import email.parser
from email.header import decode_header

import httpx
import pymysql
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common import NoSuchElementException, TimeoutException

from ixbrowser_local_api import IXBrowserClient
from examples.auto_profile_setup import read_data_file, handle_failed_profile

#主要用来更换推特的邮箱账号

# MySQL数据库配置
MYSQL_CONFIG = {
    'host': '**************',
    'port': 3306,
    'user': 'smmbesma',
    'password': 'FRSPjNiryhfyeiP6',
    'database': 'smmbesma',  # 假设数据库名，如果不同请修改
    'charset': 'utf8mb4'
}

# IMAP邮箱配置
IMAP_CONFIG = {
    'host': 'imap.firstmail.ltd',
    'port': 993,
    'use_ssl': True
}


def connect_mysql():
    """连接MySQL数据库"""
    try:
        connection = pymysql.connect(**MYSQL_CONFIG)
        print("✅ MySQL数据库连接成功")
        return connection
    except Exception as e:
        print(f"❌ MySQL数据库连接失败: {str(e)}")
        return None


def query_email_password(email):
    """查询邮箱对应的密码"""
    connection = None
    try:
        print(f"🔍 查询邮箱密码: {email}")

        connection = connect_mysql()
        if not connection:
            return None

        cursor = connection.cursor()

        # 查询acc-twitter表中email字段匹配的记录
        query = "SELECT password FROM `acc-twitter` WHERE email = %s"
        cursor.execute(query, (email,))

        result = cursor.fetchone()

        if result:
            password = result[0]
            print(f"✅ 找到匹配的密码: {password[:3]}***{password[-3:] if len(password) > 6 else '***'}")
            return password
        else:
            print(f"❌ 未找到邮箱 {email} 对应的密码")
            return None

    except Exception as e:
        print(f"❌ 查询数据库时出错: {str(e)}")
        return None
    finally:
        if connection:
            connection.close()


def extract_twitter_username(driver):
    """提取推特用户名"""
    try:
        print(f"🔍 尝试提取推特用户名...")

        # 方法1: 先尝试从首页获取个人资料链接
        try:
            print(f"🔍 方法1: 从首页获取个人资料链接...")
            driver.get("https://twitter.com/home")
            time.sleep(8)

            # 查找个人资料链接 - 多种可能的选择器
            profile_selectors = [
                "//a[@data-testid='AppTabBar_Profile_Link']",
                "//a[contains(@href, '/') and contains(@aria-label, 'Profile')]",
                "//a[contains(@href, '/') and @role='link']//span[text()='Profile']/..",
                "//nav//a[contains(@href, '/') and not(contains(@href, '/home')) and not(contains(@href, '/notifications')) and not(contains(@href, '/messages'))]"
            ]

            for selector in profile_selectors:
                try:
                    print(f"🔍 尝试个人资料选择器: {selector}")
                    elements = driver.find_elements(By.XPATH, selector)

                    for element in elements:
                        href = element.get_attribute('href')
                        if href and 'twitter.com/' in href:
                            # 提取用户名
                            username = href.split('/')[-1]
                            if username and username not in ['home', 'notifications', 'messages', 'search', 'explore', 'settings', 'i']:
                                print(f"✅ 从个人资料链接提取用户名: {username}")
                                return username

                except Exception as e:
                    print(f"⚠️ 选择器 {selector} 失败: {str(e)}")
                    continue

        except Exception as e:
            print(f"⚠️ 从首页提取用户名失败: {str(e)}")

        # 方法2: 访问个人资料页面
        try:
            print(f"🔍 方法2: 访问个人资料页面...")

            # 先点击个人资料按钮
            try:
                profile_button = driver.find_element(By.XPATH, "//a[@data-testid='AppTabBar_Profile_Link']")
                profile_button.click()
                time.sleep(8)

                # 从URL中提取用户名
                current_url = driver.current_url
                print(f"📍 个人资料页面URL: {current_url}")

                if 'twitter.com/' in current_url or 'x.com/' in current_url:
                    username = current_url.split('/')[-1]
                    if username and username not in ['home', 'notifications', 'messages', 'search', 'explore', 'settings', 'i']:
                        print(f"✅ 从个人资料页面URL提取用户名: {username}")
                        return username

            except Exception as e:
                print(f"⚠️ 点击个人资料按钮失败: {str(e)}")

        except Exception as e:
            print(f"⚠️ 访问个人资料页面失败: {str(e)}")

        # 方法3: 访问设置页面获取用户名
        try:
            print(f"🔍 方法3: 从设置页面获取用户名...")
            driver.get("https://twitter.com/settings/profile")
            time.sleep(10)

            # 尝试多种选择器来获取用户名输入框
            username_selectors = [
                "//input[@name='username']",
                "//input[@data-testid='ocfSettingsListUsername']",
                "//input[contains(@placeholder, 'username')]",
                "//input[contains(@aria-label, 'username')]",
                "//input[@type='text' and contains(@value, '@')]"
            ]

            for selector in username_selectors:
                try:
                    print(f"🔍 尝试用户名输入框选择器: {selector}")
                    element = driver.find_element(By.XPATH, selector)
                    username = element.get_attribute('value').strip()

                    if username:
                        # 去掉可能的@符号
                        if username.startswith('@'):
                            username = username[1:]
                        print(f"✅ 从设置页面提取用户名: {username}")
                        return username

                except Exception as e:
                    print(f"⚠️ 选择器 {selector} 失败: {str(e)}")
                    continue

        except Exception as e:
            print(f"⚠️ 从设置页面提取用户名失败: {str(e)}")

        # 方法4: 尝试从页面中的@用户名文本提取
        try:
            print(f"🔍 方法4: 从页面文本中查找@用户名...")
            driver.get("https://twitter.com/home")
            time.sleep(5)

            # 查找包含@的文本元素
            username_elements = driver.find_elements(By.XPATH, "//span[starts-with(text(), '@')]")

            for element in username_elements:
                username_text = element.text.strip()
                if username_text.startswith('@') and len(username_text) > 1:
                    username = username_text[1:]  # 去掉@符号
                    # 验证用户名格式（只包含字母、数字、下划线）
                    if username.replace('_', '').replace('-', '').isalnum():
                        print(f"✅ 从页面文本提取用户名: {username}")
                        return username

        except Exception as e:
            print(f"⚠️ 从页面文本提取用户名失败: {str(e)}")

        print(f"❌ 所有方法都无法提取推特用户名")
        return None

    except Exception as e:
        print(f"❌ 提取用户名时出错: {str(e)}")
        return None


def query_username_password(username):
    """根据用户名查询密码（模糊匹配）"""
    connection = None
    try:
        print(f"🔍 根据用户名查询密码: {username}")

        connection = connect_mysql()
        if not connection:
            return None

        cursor = connection.cursor()

        # 使用LIKE进行模糊查询，匹配包含用户名的URL
        query = "SELECT password FROM `acc-twitter` WHERE username LIKE %s"
        search_pattern = f"%{username}%"
        cursor.execute(query, (search_pattern,))

        result = cursor.fetchone()

        if result:
            password = result[0]
            print(f"✅ 根据用户名找到匹配的密码: {password[:3]}***{password[-3:] if len(password) > 6 else '***'}")
            return password
        else:
            print(f"❌ 未找到用户名 {username} 对应的密码")
            return None

    except Exception as e:
        print(f"❌ 根据用户名查询数据库时出错: {str(e)}")
        return None
    finally:
        if connection:
            connection.close()


def input_password_in_popup(driver, password):
    """在弹出的密码输入框中输入密码"""
    try:
        print(f"🔑 尝试在弹窗中输入密码...")

        # 等待密码输入框出现 - 增加等待时间
        print("⏳ 等待密码输入框出现...")
        time.sleep(8)  # 增加到8秒

        # 可能的密码输入框选择器（基于实际元素优化）
        password_selectors = [
            # 基于你提供的实际元素，最精确的选择器
            "//input[@name='password' and @type='password']",
            "//input[@type='password' and @autocomplete='on']",
            "//input[@type='password']",
            "//input[@name='password']",

            # 基于实际class的选择器
            "//input[contains(@class, 'r-30o5oe') and @type='password']",
            "//input[contains(@class, 'r-1dz5y72') and @type='password']",

            # 其他备选选择器
            "//input[contains(@placeholder, 'password')]",
            "//input[contains(@placeholder, 'Password')]",
            "//input[contains(@class, 'password')]",
            "//input[@autocomplete='current-password']",
            "//input[@data-testid='password']",
            "//input[contains(@aria-label, 'password')]",
            "//input[contains(@aria-label, 'Password')]"
        ]

        password_input = None
        max_attempts = 3  # 最多尝试3次

        for attempt in range(max_attempts):
            print(f"🔍 第 {attempt + 1} 次尝试查找密码输入框...")

            for selector in password_selectors:
                try:
                    print(f"🔍 尝试密码输入框选择器: {selector}")
                    elements = driver.find_elements(By.XPATH, selector)

                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            password_input = element
                            print(f"✅ 找到密码输入框: {selector}")
                            break

                    if password_input:
                        break
                except Exception as e:
                    print(f"⚠️ 选择器失败: {selector} - {str(e)}")
                    continue

            if password_input:
                break

            if attempt < max_attempts - 1:
                print(f"⏳ 未找到密码输入框，等待5秒后重试...")
                time.sleep(5)

        if not password_input:
            print("❌ 多次尝试后仍未找到密码输入框")
            print("💡 可能原因：")
            print("   1. 页面还在加载中")
            print("   2. 弹窗还未完全出现")
            print("   3. 需要先点击其他元素")
            return False

        # 清空输入框并输入密码
        try:
            password_input.clear()
            password_input.send_keys(password)
            print("✅ 密码输入成功")
            time.sleep(2)  # 等待密码输入完成

            # 查找并点击确认按钮
            confirm_selectors = [
                # 基于你提供的元素结构，优先尝试Next按钮
                "//span[contains(text(), 'Next')]",
                "//span[contains(text(), 'Next')]/..",
                "//span[contains(text(), 'Next')]/../../..",
                "//div[contains(@class, 'css-146c3p1') and .//span[contains(text(), 'Next')]]",
                "//div[@role='button' and .//span[contains(text(), 'Next')]]",

                # 其他常见的确认按钮
                "//button[contains(text(), 'Next')]",
                "//button[contains(text(), 'Confirm')]",
                "//button[contains(text(), 'Submit')]",
                "//button[contains(text(), 'Continue')]",
                "//button[contains(text(), 'Verify')]",
                "//button[@type='submit']",
                "//button[contains(@class, 'confirm')]",
                "//button[contains(@class, 'submit')]",

                # 通过span文本查找父元素
                "//span[contains(text(), 'Confirm')]/..",
                "//span[contains(text(), 'Submit')]/..",
                "//span[contains(text(), 'Continue')]/..",

                # div作为按钮的情况
                "//div[@role='button' and contains(text(), 'Next')]",
                "//div[@role='button' and contains(text(), 'Confirm')]",
                "//div[@role='button' and contains(text(), 'Submit')]",
                "//div[@role='button' and contains(text(), 'Continue')]",

                # 通过data-testid和aria-label
                "//button[contains(@data-testid, 'confirm')]",
                "//button[contains(@data-testid, 'submit')]",
                "//button[contains(@data-testid, 'next')]",
                "//button[contains(@aria-label, 'confirm')]",
                "//button[contains(@aria-label, 'Confirm')]",
                "//button[contains(@aria-label, 'Next')]",

                # 通用的可点击元素包含Next文本
                "//*[@role='button' and .//text()[contains(., 'Next')]]",
                "//*[contains(@class, 'css-146c3p1') and .//text()[contains(., 'Next')]]"
            ]

            confirm_button = None
            for selector in confirm_selectors:
                try:
                    print(f"🔍 尝试确认按钮选择器: {selector}")
                    elements = driver.find_elements(By.XPATH, selector)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            button_text = element.text.strip()
                            print(f"✅ 找到可用确认按钮: {selector}, 文本: '{button_text}'")
                            confirm_button = element
                            break
                    if confirm_button:
                        break
                except Exception as e:
                    print(f"⚠️ 确认按钮选择器失败: {selector} - {str(e)}")
                    continue

            if confirm_button:
                try:
                    # 滚动到按钮位置
                    driver.execute_script("arguments[0].scrollIntoView(true);", confirm_button)
                    time.sleep(1)

                    # 点击确认按钮
                    confirm_button.click()
                    print("✅ 成功点击确认按钮")
                    time.sleep(5)  # 等待页面跳转到下一步
                    return True
                except Exception as e:
                    print(f"❌ 点击确认按钮失败: {str(e)}")
                    # 尝试使用JavaScript点击
                    try:
                        driver.execute_script("arguments[0].click();", confirm_button)
                        print("✅ 使用JavaScript成功点击确认按钮")
                        time.sleep(5)
                        return True
                    except Exception as js_error:
                        print(f"❌ JavaScript点击确认按钮也失败: {str(js_error)}")
                        return False
            else:
                print("❌ 未找到确认按钮")
                print("💡 请手动点击确认按钮继续")
                return False

        except Exception as e:
            print(f"❌ 输入密码失败: {str(e)}")
            return False

    except Exception as e:
        print(f"❌ 在弹窗中输入密码时出错: {str(e)}")
        return False


def switch_ip():
    """切换IP地址"""
    try:
        print("🔄 正在切换IP地址...")
        with httpx.Client(timeout=30.0) as http_client:
            response = http_client.get(
                "https://mproxy.vn/capi/_iCp27tGi46qKNBKo0TyUArkI-8rPGrkK7BB83SMAts/key/cWt6bAQdTeapT/resetIp"
            )
            if response.status_code == 200:
                print("✅ IP切换成功")
                time.sleep(10)  # 等待10秒让IP生效
                return True
            else:
                print(f"❌ IP切换失败，状态码: {response.status_code}")
                return False
    except Exception as e:
        print(f"❌ IP切换请求出错: {str(e)}")
        return False


def get_profile_list(name=None, timeout=20.0):
    """获取配置文件列表，包含备注信息"""
    client = None
    try:
        client = httpx.Client(timeout=timeout)
        url = "http://localhost:53200/api/v2/profile-list"
        params = {"page": 1, "limit": 100}

        if name:
            params["name"] = name

        response = client.post(url, json=params)
        data = response.json()

        if response.status_code == 200 and data.get("error", {}).get("code") == 0:
            profiles = data.get("data", {}).get("data", [])

            # 调试：显示返回的profile数据结构
            if profiles and len(profiles) > 0:
                first_profile = profiles[0]
                print(f"📋 Profile数据字段: {list(first_profile.keys())}")

                # 检查是否有note字段
                if 'note' in first_profile:
                    note_content = first_profile.get('note', '')
                    print(f"📝 找到note字段，内容长度: {len(note_content)} 字符")
                    if note_content:
                        print(f"📝 备注内容预览: {note_content[:100]}{'...' if len(note_content) > 100 else ''}")
                    else:
                        print(f"📝 note字段为空")
                else:
                    print(f"❌ 未找到note字段")

            return profiles, None, None

        error_code = data.get("error", {}).get("code")
        error_message = data.get("error", {}).get("message")
        return None, error_code, error_message

    except Exception as e:
        return None, None, str(e)
    finally:
        if client:
            client.close()


def get_profile_remark(profile_id, timeout=20.0):
    """获取配置文件的备注信息"""
    client = None
    try:
        print(f"🔍 正在获取Profile {profile_id} 的备注信息...")

        client = httpx.Client(timeout=timeout)
        url = "http://localhost:53200/api/v2/profile-detail"
        params = {"profile_id": profile_id}

        print(f"📡 发送请求到: {url}")
        print(f"📋 请求参数: {params}")

        response = client.post(url, json=params)

        print(f"📊 响应状态码: {response.status_code}")

        try:
            data = response.json()
            print(f"📄 响应数据结构: {list(data.keys()) if isinstance(data, dict) else type(data)}")
        except:
            print(f"❌ 响应不是有效的JSON格式")
            print(f"📄 原始响应: {response.text[:200]}...")
            return ""

        if response.status_code == 200:
            error_info = data.get("error", {})
            error_code = error_info.get("code", -1)

            print(f"🔍 错误代码: {error_code}")

            if error_code == 0:
                profile_data = data.get("data", {})
                print(f"📋 Profile数据字段: {list(profile_data.keys()) if isinstance(profile_data, dict) else 'Not a dict'}")

                remark = profile_data.get("remark", "")

                if remark:
                    print(f"✅ 成功读取到备注信息 ({len(remark)} 字符)")
                    print(f"📝 备注内容: {remark[:100]}{'...' if len(remark) > 100 else ''}")
                    return remark
                else:
                    print(f"⚠️ 备注字段为空")
                    return ""
            else:
                error_message = error_info.get("message", "未知错误")
                print(f"❌ API返回错误: {error_message}")
                return ""
        else:
            print(f"❌ HTTP请求失败，状态码: {response.status_code}")
            print(f"📄 响应内容: {response.text[:200]}...")
            return ""

    except Exception as e:
        print(f"❌ 获取备注时出错: {str(e)}")
        import traceback
        print(f"🔍 详细错误信息: {traceback.format_exc()}")
        return ""
    finally:
        if client:
            client.close()


def extract_backup_email_from_remark(remark):
    """从备注中提取备用邮箱地址"""
    try:
        print(f"🔍 从备注中提取备用邮箱...")

        # 首先尝试解析JSON格式
        try:
            remark_data = json.loads(remark)
            if isinstance(remark_data, dict):
                # 查找new_email字段
                if 'new_email' in remark_data:
                    backup_email = remark_data['new_email']
                    print(f"✅ 从JSON中找到备用邮箱: {backup_email}")
                    return backup_email
                else:
                    print("❌ JSON格式备注中未找到new_email字段")
        except json.JSONDecodeError:
            print("💡 备注不是JSON格式，尝试正则表达式解析...")

        # 如果不是JSON格式，使用正则表达式匹配邮箱地址
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        emails = re.findall(email_pattern, remark)

        if emails:
            backup_email = emails[0]  # 取第一个邮箱作为备用邮箱
            print(f"✅ 找到备用邮箱: {backup_email}")
            return backup_email
        else:
            print("❌ 备注中未找到邮箱地址")
            return None

    except Exception as e:
        print(f"❌ 提取备用邮箱时出错: {str(e)}")
        return None


def extract_backup_email_password_from_remark(remark):
    """从备注中提取备用邮箱的密码"""
    try:
        print(f"🔍 从备注中提取备用邮箱密码...")

        # 首先尝试解析JSON格式
        try:
            remark_data = json.loads(remark)
            if isinstance(remark_data, dict):
                # 查找new_email_password字段
                if 'new_email_password' in remark_data:
                    password = remark_data['new_email_password']
                    print(f"✅ 从JSON中找到备用邮箱密码: {password[:3]}***{password[-3:] if len(password) > 6 else '***'}")
                    return password
                else:
                    print("❌ JSON格式备注中未找到new_email_password字段")
        except json.JSONDecodeError:
            print("💡 备注不是JSON格式，尝试正则表达式解析...")

        # 如果不是JSON格式，使用原有的正则表达式方法
        # 方法1: 查找邮箱后面的密码 (email:password格式)
        email_password_pattern = r'([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}):([^\s]+)'
        match = re.search(email_password_pattern, remark)

        if match:
            email, password = match.groups()
            print(f"✅ 找到备用邮箱密码: {password[:3]}***{password[-3:] if len(password) > 6 else '***'}")
            return password

        # 方法2: 查找邮箱后面的密码 (email password格式，空格分隔)
        email_space_password_pattern = r'([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})\s+([^\s]+)'
        match = re.search(email_space_password_pattern, remark)

        if match:
            email, password = match.groups()
            print(f"✅ 找到备用邮箱密码: {password[:3]}***{password[-3:] if len(password) > 6 else '***'}")
            return password

        # 方法3: 如果没有找到，尝试查找可能的密码模式
        # 假设密码是备注中的非邮箱字符串
        words = remark.split()
        for word in words:
            if '@' not in word and len(word) >= 6:  # 假设密码至少6位且不包含@
                print(f"⚠️ 尝试使用可能的密码: {word[:3]}***{word[-3:] if len(word) > 6 else '***'}")
                return word

        print("❌ 备注中未找到邮箱密码")
        return None

    except Exception as e:
        print(f"❌ 提取备用邮箱密码时出错: {str(e)}")
        return None


def connect_imap(email_address, password):
    """连接IMAP邮箱"""
    try:
        print(f"📧 连接IMAP邮箱: {email_address}")

        # 连接IMAP服务器
        mail = imaplib.IMAP4_SSL(IMAP_CONFIG['host'], IMAP_CONFIG['port'])

        # 登录
        mail.login(email_address, password)
        print("✅ IMAP邮箱登录成功")

        return mail

    except Exception as e:
        print(f"❌ IMAP邮箱连接失败: {str(e)}")
        return None


def get_verification_code_from_email(email_address, password, max_wait_time=300):
    """从邮箱中获取Twitter验证码"""
    try:
        print(f"📧 开始从邮箱获取验证码...")
        print(f"⏳ 最多等待 {max_wait_time} 秒...")

        start_time = time.time()

        while time.time() - start_time < max_wait_time:
            try:
                # 连接邮箱
                mail = connect_imap(email_address, password)
                if not mail:
                    print("❌ 邮箱连接失败，等待重试...")
                    time.sleep(10)
                    continue

                # 选择收件箱
                mail.select('inbox')

                # 搜索来自Twitter的最新邮件
                search_criteria = [
                    '(FROM "<EMAIL>")',
                    '(FROM "<EMAIL>")',
                    '(FROM "<EMAIL>")',
                    '(SUBJECT "verification")',
                    '(SUBJECT "verify")',
                    '(SUBJECT "code")'
                ]

                verification_code = None

                for criteria in search_criteria:
                    try:
                        # 搜索邮件
                        status, messages = mail.search(None, criteria)

                        if status == 'OK' and messages[0]:
                            # 获取最新的邮件ID
                            email_ids = messages[0].split()
                            if email_ids:
                                latest_email_id = email_ids[-1]

                                # 获取邮件内容
                                status, msg_data = mail.fetch(latest_email_id, '(RFC822)')

                                if status == 'OK':
                                    try:
                                        # 解析邮件
                                        raw_email = msg_data[0][1]
                                        print(f"📧 原始邮件数据类型: {type(raw_email)}")

                                        if isinstance(raw_email, bytes):
                                            email_message = email.parser.BytesParser().parsebytes(raw_email)
                                        else:
                                            email_message = email.parser.Parser().parsestr(str(raw_email))

                                        # 获取邮件主题
                                        subject_header = email_message.get("Subject", "")
                                        if subject_header:
                                            try:
                                                subject_parts = decode_header(subject_header)
                                                subject = ""
                                                for part, encoding in subject_parts:
                                                    if isinstance(part, bytes):
                                                        if encoding:
                                                            subject += part.decode(encoding)
                                                        else:
                                                            subject += part.decode('utf-8', errors='ignore')
                                                    else:
                                                        subject += str(part)
                                            except Exception as e:
                                                subject = str(subject_header)
                                                print(f"⚠️ 解析邮件主题失败: {str(e)}")
                                        else:
                                            subject = "无主题"

                                        print(f"📧 邮件主题: {subject}")

                                        # 获取发件人
                                        sender = email_message.get("From", "未知发件人")
                                        print(f"📧 发件人: {sender}")

                                        # 获取邮件内容
                                        email_body = ""
                                        email_html = ""

                                        if email_message.is_multipart():
                                            print("📧 多部分邮件，正在解析各部分...")
                                            for i, part in enumerate(email_message.walk()):
                                                content_type = part.get_content_type()
                                                print(f"📧 部分 {i}: {content_type}")

                                                if content_type == "text/plain":
                                                    try:
                                                        payload = part.get_payload(decode=True)
                                                        if payload:
                                                            if isinstance(payload, bytes):
                                                                email_body = payload.decode('utf-8', errors='ignore')
                                                            else:
                                                                email_body = str(payload)
                                                            print(f"📧 找到纯文本内容，长度: {len(email_body)}")
                                                    except Exception as e:
                                                        print(f"⚠️ 解析纯文本部分失败: {str(e)}")

                                                elif content_type == "text/html":
                                                    try:
                                                        payload = part.get_payload(decode=True)
                                                        if payload:
                                                            if isinstance(payload, bytes):
                                                                email_html = payload.decode('utf-8', errors='ignore')
                                                            else:
                                                                email_html = str(payload)
                                                            print(f"📧 找到HTML内容，长度: {len(email_html)}")
                                                    except Exception as e:
                                                        print(f"⚠️ 解析HTML部分失败: {str(e)}")
                                        else:
                                            print("📧 单部分邮件，直接解析...")
                                            try:
                                                payload = email_message.get_payload(decode=True)
                                                if payload:
                                                    if isinstance(payload, bytes):
                                                        email_body = payload.decode('utf-8', errors='ignore')
                                                    else:
                                                        email_body = str(payload)
                                                    print(f"📧 邮件内容长度: {len(email_body)}")
                                            except Exception as e:
                                                print(f"⚠️ 解析邮件内容失败: {str(e)}")
                                                # 尝试直接获取payload
                                                email_body = str(email_message.get_payload())

                                        # 打印邮件内容（前500字符）
                                        if email_body:
                                            print(f"📧 邮件纯文本内容（前500字符）:")
                                            print("=" * 50)
                                            print(email_body[:500])
                                            print("=" * 50)

                                            # 从纯文本中提取验证码
                                            verification_code = extract_verification_code_from_text(email_body)
                                            if verification_code:
                                                print(f"✅ 从纯文本中找到验证码: {verification_code}")

                                                # 删除收件箱中的所有邮件
                                                delete_all_emails(mail)

                                                mail.close()
                                                mail.logout()
                                                return verification_code

                                        # 如果纯文本没有找到验证码，尝试HTML内容
                                        if email_html and not verification_code:
                                            print(f"📧 邮件HTML内容（前500字符）:")
                                            print("=" * 50)
                                            print(email_html[:500])
                                            print("=" * 50)

                                            # 从HTML中提取验证码
                                            verification_code = extract_verification_code_from_text(email_html)
                                            if verification_code:
                                                print(f"✅ 从HTML中找到验证码: {verification_code}")

                                                # 删除收件箱中的所有邮件
                                                delete_all_emails(mail)

                                                mail.close()
                                                mail.logout()
                                                return verification_code

                                        # 如果都没有找到，打印完整内容用于调试
                                        if not verification_code:
                                            print(f"❌ 未在邮件中找到验证码")
                                            if email_body:
                                                print(f"📧 完整纯文本内容:")
                                                print(email_body)
                                            if email_html:
                                                print(f"📧 完整HTML内容:")
                                                print(email_html)

                                    except Exception as e:
                                        print(f"❌ 解析邮件时出错: {str(e)}")
                                        import traceback
                                        print(f"🔍 详细错误信息: {traceback.format_exc()}")
                                        continue
                    except Exception as e:
                        print(f"⚠️ 搜索邮件时出错: {str(e)}")
                        continue

                # 关闭连接
                mail.close()
                mail.logout()

                if verification_code:
                    return verification_code

                print(f"⏳ 未找到验证码，等待10秒后重试...")
                time.sleep(10)

            except Exception as e:
                print(f"⚠️ 获取邮件时出错: {str(e)}")
                time.sleep(10)
                continue

        print(f"❌ 超时未收到验证码")
        return None

    except Exception as e:
        print(f"❌ 获取验证码时出错: {str(e)}")
        return None


def delete_all_emails(mail):
    """删除收件箱中的所有邮件"""
    try:
        print(f"🗑️ 开始删除收件箱中的所有邮件...")

        # 搜索所有邮件
        status, messages = mail.search(None, 'ALL')

        if status == 'OK' and messages[0]:
            email_ids = messages[0].split()
            email_count = len(email_ids)

            if email_count > 0:
                print(f"📊 找到 {email_count} 封邮件，开始删除...")

                # 标记所有邮件为删除
                for email_id in email_ids:
                    try:
                        mail.store(email_id, '+FLAGS', '\\Deleted')
                    except Exception as e:
                        print(f"⚠️ 标记邮件 {email_id.decode()} 删除失败: {str(e)}")

                # 执行删除操作
                mail.expunge()
                print(f"✅ 成功删除 {email_count} 封邮件")

                # 验证删除结果
                status, remaining_messages = mail.search(None, 'ALL')
                if status == 'OK':
                    remaining_count = len(remaining_messages[0].split()) if remaining_messages[0] else 0
                    print(f"📊 删除后剩余邮件数量: {remaining_count}")

                    if remaining_count == 0:
                        print(f"✅ 收件箱已清空")
                    else:
                        print(f"⚠️ 还有 {remaining_count} 封邮件未删除")
            else:
                print(f"📭 收件箱已经是空的")
        else:
            print(f"📭 收件箱中没有邮件")

    except Exception as e:
        print(f"❌ 删除邮件时出错: {str(e)}")
        import traceback
        print(f"🔍 详细错误信息: {traceback.format_exc()}")


def extract_verification_code_from_text(text):
    """从邮件文本中提取验证码"""
    try:
        print(f"🔍 开始从文本中提取验证码...")
        print(f"📝 文本长度: {len(text)} 字符")

        # 常见的验证码模式（按优先级排序）
        patterns = [
            # Twitter特定模式
            r'twitter.*?verification.*?code[:\s]*([0-9]{4,8})',
            r'twitter.*?code[:\s]*([0-9]{4,8})',
            r'x\.com.*?code[:\s]*([0-9]{4,8})',

            # 通用验证码模式
            r'verification\s*code[:\s]*([0-9]{4,8})',
            r'verify.*?code[:\s]*([0-9]{4,8})',
            r'confirmation\s*code[:\s]*([0-9]{4,8})',
            r'security\s*code[:\s]*([0-9]{4,8})',
            r'access\s*code[:\s]*([0-9]{4,8})',

            # 简单模式
            r'code[:\s]*([0-9]{4,8})',
            r'verify[:\s]*([0-9]{4,8})',
            r'confirm[:\s]*([0-9]{4,8})',

            # HTML模式
            r'<.*?>([0-9]{6})<.*?>',
            r'<strong>([0-9]{4,8})</strong>',
            r'<b>([0-9]{4,8})</b>',

            # 独立数字模式（最后尝试）
            r'\b([0-9]{6})\b',  # 6位数字
            r'\b([0-9]{5})\b',  # 5位数字
            r'\b([0-9]{4})\b',  # 4位数字
            r'\b([0-9]{8})\b',  # 8位数字
        ]

        for i, pattern in enumerate(patterns, 1):
            print(f"🔍 尝试模式 {i}: {pattern}")
            try:
                matches = re.findall(pattern, text, re.IGNORECASE | re.DOTALL)
                if matches:
                    print(f"✅ 模式 {i} 找到匹配: {matches}")

                    for match in matches:
                        code = match.strip()
                        # 验证码通常是4-8位数字
                        if code.isdigit() and 4 <= len(code) <= 8:
                            print(f"✅ 验证码验证通过: {code}")
                            return code
                        else:
                            print(f"⚠️ 验证码格式不符合要求: {code} (长度: {len(code)})")
                else:
                    print(f"❌ 模式 {i} 无匹配")
            except Exception as e:
                print(f"❌ 模式 {i} 执行失败: {str(e)}")
                continue

        # 如果所有模式都失败，尝试查找所有数字并分析
        print(f"🔍 尝试查找所有数字...")
        all_numbers = re.findall(r'\b\d+\b', text)
        if all_numbers:
            print(f"📊 找到的所有数字: {all_numbers}")

            # 过滤出可能的验证码
            potential_codes = [num for num in all_numbers if 4 <= len(num) <= 8]
            if potential_codes:
                print(f"🎯 可能的验证码: {potential_codes}")
                return potential_codes[0]  # 返回第一个可能的验证码

        print(f"❌ 未找到验证码")
        return None

    except Exception as e:
        print(f"❌ 提取验证码时出错: {str(e)}")
        import traceback
        print(f"🔍 详细错误信息: {traceback.format_exc()}")
        return None


def fill_backup_email(driver, backup_email):
    """填写备用邮箱地址"""
    try:
        print(f"📧 尝试填写备用邮箱: {backup_email}")

        # 等待页面加载
        time.sleep(3)

        # 可能的备用邮箱输入框选择器（基于实际元素优化）
        backup_email_selectors = [
            # 基于你提供的实际元素，最精确的选择器
            "//input[@name='email' and @type='email' and @autocomplete='email']",
            "//input[@name='email' and @type='email']",
            "//input[@autocomplete='email' and @type='email']",

            # 最常见的邮箱输入框
            "//input[@type='email']",
            "//input[@name='email']",
            "//input[@autocomplete='email']",

            # 通过class查找（使用实际的class）
            "//input[contains(@class, 'r-30o5oe') and @type='email']",
            "//input[contains(@class, 'r-1dz5y72') and @type='email']",

            # 通过placeholder查找
            "//input[contains(@placeholder, 'email')]",
            "//input[contains(@placeholder, 'Email')]",
            "//input[contains(@placeholder, 'EMAIL')]",
            "//input[contains(@placeholder, 'Enter')]",
            "//input[contains(@placeholder, 'backup')]",
            "//input[contains(@placeholder, 'recovery')]",

            # 通过data属性查找
            "//input[@data-testid='email']",
            "//input[contains(@data-testid, 'email')]",
            "//input[contains(@data-testid, 'backup')]",

            # 通过aria-label查找
            "//input[contains(@aria-label, 'email')]",
            "//input[contains(@aria-label, 'Email')]",
            "//input[contains(@aria-label, 'backup')]",
            "//input[contains(@aria-label, 'recovery')]",

            # 通用的文本输入框（作为备选）
            "//input[@type='text']",

            # 通过父元素文本查找
            "//label[contains(text(), 'email')]//input",
            "//label[contains(text(), 'Email')]//input",
            "//div[contains(text(), 'email')]//input",
            "//div[contains(text(), 'Email')]//input"
        ]

        email_input = None
        for selector in backup_email_selectors:
            try:
                print(f"🔍 尝试邮箱输入框选择器: {selector}")
                elements = driver.find_elements(By.XPATH, selector)

                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        email_input = element
                        print(f"✅ 找到邮箱输入框: {selector}")
                        break

                if email_input:
                    break

            except Exception as e:
                print(f"⚠️ 选择器失败: {selector} - {str(e)}")
                continue

        if not email_input:
            print("❌ 未找到邮箱输入框")
            return False

        # 清空输入框并输入备用邮箱
        try:
            email_input.clear()
            email_input.send_keys(backup_email)
            print("✅ 备用邮箱输入成功")
            time.sleep(1)

            # 查找并点击提交/继续按钮（基于实际元素优化）
            submit_selectors = [
                # 基于你提供的实际元素，最精确的选择器
                "//button[@data-testid='ocfEnterEmailNextLink']",
                "//button[@data-testid='ocfEnterEmailNextLink' and @type='button']",
                "//button[contains(@class, 'css-175oi2r') and @data-testid='ocfEnterEmailNextLink']",

                # 通过data-testid查找
                "//button[contains(@data-testid, 'ocfEnterEmailNextLink')]",
                "//button[contains(@data-testid, 'NextLink')]",
                "//button[contains(@data-testid, 'next')]",
                "//button[contains(@data-testid, 'Next')]",

                # 通过内部文本查找
                "//button[.//span[contains(text(), 'Next')]]",
                "//button[contains(text(), 'Next')]",
                "//button[contains(text(), 'Submit')]",
                "//button[contains(text(), 'Continue')]",
                "//button[contains(text(), 'Send')]",

                # 通过class查找
                "//button[contains(@class, 'css-175oi2r')]",
                "//button[@type='submit']",
                "//button[contains(@class, 'submit')]",

                # 通过span文本查找父按钮
                "//span[contains(text(), 'Next')]/../..",
                "//span[contains(text(), 'Submit')]/../..",
                "//div[@role='button' and contains(text(), 'Next')]",
                "//div[@role='button' and contains(text(), 'Submit')]"
            ]

            submit_button = None
            for selector in submit_selectors:
                try:
                    elements = driver.find_elements(By.XPATH, selector)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            submit_button = element
                            print(f"✅ 找到提交按钮: {selector}")
                            break
                    if submit_button:
                        break
                except:
                    continue

            if submit_button:
                try:
                    submit_button.click()
                    print("✅ 成功点击提交按钮")
                    time.sleep(3)
                    return True
                except Exception as e:
                    print(f"❌ 点击提交按钮失败: {str(e)}")
                    return False
            else:
                print("⚠️ 未找到提交按钮，邮箱已输入但需要手动提交")
                return True

        except Exception as e:
            print(f"❌ 输入备用邮箱失败: {str(e)}")
            return False

    except Exception as e:
        print(f"❌ 填写备用邮箱时出错: {str(e)}")
        return False


def fill_verification_code(driver, verification_code):
    """填写验证码"""
    try:
        print(f"🔢 尝试填写验证码: {verification_code}")

        # 等待验证码输入页面加载
        time.sleep(3)

        # 可能的验证码输入框选择器
        code_selectors = [
            "//input[@type='text']",
            "//input[@name='code']",
            "//input[@name='verification_code']",
            "//input[@placeholder*='code']",
            "//input[@placeholder*='Code']",
            "//input[contains(@class, 'code')]",
            "//input[@data-testid='code']",
            "//input[contains(@aria-label, 'code')]",
            "//input[contains(@aria-label, 'verification')]"
        ]

        code_input = None
        for selector in code_selectors:
            try:
                print(f"🔍 尝试验证码输入框选择器: {selector}")
                elements = driver.find_elements(By.XPATH, selector)

                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        code_input = element
                        print(f"✅ 找到验证码输入框: {selector}")
                        break

                if code_input:
                    break

            except Exception as e:
                print(f"⚠️ 选择器失败: {selector} - {str(e)}")
                continue

        if not code_input:
            print("❌ 未找到验证码输入框")
            return False

        # 清空输入框并输入验证码
        try:
            code_input.clear()
            code_input.send_keys(verification_code)
            print("✅ 验证码输入成功")
            time.sleep(1)

            # 查找并点击确认按钮
            confirm_selectors = [
                "//button[contains(text(), 'Verify')]",
                "//button[contains(text(), 'Confirm')]",
                "//button[contains(text(), 'Submit')]",
                "//button[contains(text(), 'Continue')]",
                "//button[@type='submit']",
                "//button[contains(@class, 'verify')]",
                "//span[contains(text(), 'Verify')]/..",
                "//div[@role='button' and contains(text(), 'Verify')]"
            ]

            confirm_button = None
            for selector in confirm_selectors:
                try:
                    elements = driver.find_elements(By.XPATH, selector)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            confirm_button = element
                            print(f"✅ 找到确认按钮: {selector}")
                            break
                    if confirm_button:
                        break
                except:
                    continue

            if confirm_button:
                try:
                    confirm_button.click()
                    print("✅ 成功点击确认按钮")
                    time.sleep(3)
                    return True
                except Exception as e:
                    print(f"❌ 点击确认按钮失败: {str(e)}")
                    return False
            else:
                print("⚠️ 未找到确认按钮，验证码已输入但需要手动确认")
                return True

        except Exception as e:
            print(f"❌ 输入验证码失败: {str(e)}")
            return False

    except Exception as e:
        print(f"❌ 填写验证码时出错: {str(e)}")
        return False


def wait_for_page_load(driver, max_wait_time=30):
    """等待页面完全加载"""
    try:
        print(f"⏳ 等待页面加载完成（最多{max_wait_time}秒）...")
        
        # 等待页面基本元素加载
        WebDriverWait(driver, max_wait_time).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )
        
        # 检查页面是否包含基本的内容
        start_time = time.time()
        while time.time() - start_time < max_wait_time:
            page_source = driver.page_source.lower()
            
            # 检查是否有基本结构
            if len(page_source) > 1000:  # 页面有基本内容
                print("✅ 页面基本结构加载完成")
                time.sleep(3)  # 额外等待3秒确保动态内容加载
                return True
            
            time.sleep(2)
        
        print("⚠️ 页面加载超时，但继续处理")
        return True
        
    except TimeoutException:
        print("❌ 页面加载超时")
        return False
    except Exception as e:
        print(f"❌ 等待页面加载时出错: {str(e)}")
        return False


def read_twitter_tokens(token_file='twitter.txt'):
    """读取Twitter token文件"""
    tokens = []
    try:
        if os.path.exists(token_file):
            with open(token_file, 'r', encoding='utf-8') as f:
                for line in f:
                    token = line.strip()
                    if token:  # 忽略空行
                        tokens.append(token)
        print(f"📋 从 {token_file} 读取到 {len(tokens)} 个token")
        return tokens
    except Exception as e:
        print(f"❌ 读取token文件失败: {str(e)}")
        return []


def save_twitter_tokens(tokens, token_file='twitter.txt'):
    """保存Twitter token到文件"""
    try:
        with open(token_file, 'w', encoding='utf-8') as f:
            for token in tokens:
                f.write(token + '\n')
        print(f"💾 已保存 {len(tokens)} 个token到 {token_file}")
        return True
    except Exception as e:
        print(f"❌ 保存token文件失败: {str(e)}")
        return False


def remove_used_token_from_file(used_token, token_file='twitter.txt'):
    """实时从token文件中移除已使用的token"""
    try:
        # 读取当前所有token
        current_tokens = read_twitter_tokens(token_file)
        
        # 移除已使用的token
        if used_token in current_tokens:
            current_tokens.remove(used_token)
            
            # 立即保存到文件
            save_twitter_tokens(current_tokens, token_file)
            print(f"🗑️ 已从文件中实时移除使用过的token: {used_token[:20]}...")
            return True
        else:
            print(f"⚠️ 要移除的token不在文件中: {used_token[:20]}...")
            return False
            
    except Exception as e:
        print(f"❌ 实时移除token时出错: {str(e)}")
        return False


def login_twitter_with_token(driver, token, retry_count=2):
    """使用token登录Twitter"""
    for attempt in range(retry_count):
        try:
            print(f"🔑 尝试使用token登录Twitter...（第{attempt + 1}/{retry_count}次尝试）")
            
            # 先访问Twitter获取域名cookie作用域
            driver.get("https://x.com")
            
            # 等待页面加载
            if not wait_for_page_load(driver, max_wait_time=20):
                if attempt < retry_count - 1:
                    print("⚠️ 页面加载失败，重试...")
                    continue
                else:
                    print("❌ 页面加载失败，无法设置token")
                    return False
            
            # 清除现有cookies
            print("🧹 清除现有cookies...")
            driver.delete_all_cookies()
            time.sleep(2)
            
            # 添加auth_token cookie
            try:
                driver.add_cookie({
                    'name': 'auth_token',
                    'value': token.replace('"', ''),
                    'domain': '.x.com',
                    'path': '/',
                    'secure': True
                })
                print("🍪 已设置auth_token cookie")
            except Exception as cookie_error:
                print(f"❌ 设置cookie失败: {str(cookie_error)}")
                if attempt < retry_count - 1:
                    continue
                else:
                    return False
            
            # 刷新页面完成登录
            print("🔄 刷新页面完成登录...")
            driver.refresh()
            
            # 等待登录完成
            if not wait_for_page_load(driver, max_wait_time=20):
                if attempt < retry_count - 1:
                    print("⚠️ 登录后页面加载失败，重试...")
                    continue
                else:
                    print("❌ 登录后页面加载失败")
                    return False
            
            # 简单检查是否登录成功（检查是否有登录状态的元素）
            try:
                # 查找一些登录后才有的元素
                login_indicators = [
                    "//a[@href='/compose/post']",
                    "//div[@data-testid='SideNav_AccountSwitcher_Button']",
                    "//nav[@role='navigation']"
                ]
                
                for indicator in login_indicators:
                    elements = driver.find_elements(By.XPATH, indicator)
                    if elements:
                        print("✅ Twitter token登录成功")
                        return True
                
                print("❌ Twitter token登录失败")
                if attempt < retry_count - 1:
                    print("🔄 登录验证失败，重试...")
                    time.sleep(3)
                    continue
                else:
                    return False
                    
            except Exception as check_error:
                print(f"⚠️ 登录状态检查出错: {str(check_error)}")
                if attempt < retry_count - 1:
                    continue
                else:
                    return False
                    
        except Exception as e:
            print(f"❌ Twitter token登录过程中出错: {str(e)}")
            if attempt < retry_count - 1:
                print("🔄 出现异常，重试...")
                time.sleep(5)
                continue
            else:
                return False
    
    return False


def extract_email_from_settings(driver):
    """从Twitter设置页面提取邮箱地址"""
    try:
        print("📧 正在访问Twitter邮箱设置页面...")
        
        # 访问邮箱设置页面
        driver.get("https://x.com/settings/email")
        
        # 等待页面加载
        if not wait_for_page_load(driver):
            print("❌ 邮箱设置页面加载失败")
            return None
        
        print("✅ 邮箱设置页面加载成功")
        
        # 查找邮箱输入框
        email_selectors = [
            "//input[@name='current_email']",
            "//input[@type='email']",
            "//input[contains(@class, 'r-30o5oe') and contains(@class, 'r-1dz5y72')]"
        ]
        
        for selector in email_selectors:
            try:
                print(f"🔍 尝试选择器: {selector}")
                email_elements = driver.find_elements(By.XPATH, selector)
                
                for element in email_elements:
                    email_value = element.get_attribute('value')
                    if email_value and '@' in email_value:
                        print(f"✅ 成功提取邮箱: {email_value}")
                        return email_value
                        
            except Exception as e:
                print(f"⚠️ 选择器失败: {selector} - {str(e)}")
                continue
        
        print("❌ 未找到邮箱地址")
        return None
        
    except Exception as e:
        print(f"❌ 提取邮箱时出错: {str(e)}")
        return None


def click_update_email_button(driver, email_address, backup_email=None, backup_email_password=None, twitter_password=None):
    """点击更新邮箱地址按钮并完成完整的验证流程"""
    try:
        print("🔍 查找更新邮箱地址按钮...")

        # 查找更新邮箱按钮
        update_button_selectors = [
            "//span[contains(text(), 'Update email address')]",
            "//button[contains(text(), 'Update email address')]",
            "//span[@class='css-1jxf684 r-bcqeeo r-1ttztb7 r-qvutc0 r-poiln3' and contains(text(), 'Update email address')]"
        ]

        for selector in update_button_selectors:
            try:
                print(f"🔍 尝试选择器: {selector}")
                elements = driver.find_elements(By.XPATH, selector)

                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        # 滚动到按钮位置
                        driver.execute_script("arguments[0].scrollIntoView(true);", element)
                        time.sleep(1)

                        # 点击按钮
                        element.click()
                        print("✅ 成功点击更新邮箱地址按钮")
                        print("⏳ 等待弹窗出现...")
                        time.sleep(8)  # 增加等待时间让弹窗完全出现

                        # 使用传入的密码或查询数据库获取密码
                        if twitter_password:
                            password = twitter_password
                            print(f"✅ 使用已找到的Twitter密码: {password[:3]}***{password[-3:] if len(password) > 6 else '***'}")
                        else:
                            password = query_email_password(email_address)

                        if password:
                            # 自动输入密码
                            password_success = input_password_in_popup(driver, password)
                            if password_success:
                                print("✅ 密码自动输入和确认按钮点击成功")

                                # 等待页面跳转到备用邮箱输入页面
                                print("⏳ 等待页面跳转到备用邮箱输入页面...")
                                time.sleep(8)  # 修改等待时间

                                # 如果有备用邮箱，自动填写
                                if backup_email:
                                    print(f"📧 开始填写备用邮箱: {backup_email}")
                                    backup_email_success = fill_backup_email(driver, backup_email)

                                    if backup_email_success:
                                        print("✅ 备用邮箱填写成功")
                                        time.sleep(20)
                                        # 如果有备用邮箱密码，尝试获取验证码
                                        if backup_email_password:
                                            print("📧 开始从备用邮箱获取验证码...")
                                            verification_code = get_verification_code_from_email(
                                                backup_email, backup_email_password
                                            )

                                            if verification_code:
                                                print(f"✅ 成功获取验证码: {verification_code}")

                                                # 等待验证码输入页面
                                                time.sleep(3)

                                                # 自动填写验证码
                                                code_success = fill_verification_code(driver, verification_code)

                                                if code_success:
                                                    print("✅ 验证码填写成功，邮箱更新流程完成")
                                                    return True
                                                else:
                                                    print("❌ 验证码填写失败，需要手动输入")
                                                    return True
                                            else:
                                                print("❌ 未获取到验证码，需要手动处理")
                                                return True
                                        else:
                                            print("❌ 未找到备用邮箱密码，无法自动获取验证码")
                                            print("💡 请手动查收验证码并输入")
                                            return True
                                    else:
                                        print("❌ 备用邮箱填写失败，需要手动输入")
                                        return True
                                else:
                                    print("❌ 未找到备用邮箱，需要手动输入")
                                    return True
                            else:
                                print("❌ 密码自动输入失败，需要手动输入")
                                return True  # 弹窗已出现，即使密码输入失败也算成功
                        else:
                            print("❌ 未找到对应密码，需要手动输入")
                            return True  # 弹窗已出现

            except Exception as e:
                print(f"⚠️ 选择器失败: {selector} - {str(e)}")
                continue

        print("❌ 未找到更新邮箱地址按钮")
        return False

    except Exception as e:
        print(f"❌ 点击更新邮箱按钮时出错: {str(e)}")
        return False


def log_email_result(wallet_address, email, success, error_msg=None, log_file='email_extraction.log'):
    """记录邮箱提取结果到日志文件"""
    try:
        from datetime import datetime

        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        status = "成功" if success else "失败"

        log_entry = f"[{timestamp}] 钱包: {wallet_address} | 邮箱: {email or 'N/A'} | 状态: {status}"
        if error_msg:
            log_entry += f" | 原因: {error_msg}"
        log_entry += "\n"

        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(log_entry)

        print(f"📝 提取结果已记录到 {log_file}")

    except Exception as e:
        print(f"⚠️ 记录提取结果失败: {str(e)}")


def process_profile(client, profile_data, available_tokens):
    """
    处理单个配置文件：检查邮箱，如果需要则使用新token登录

    Args:
        client: IXBrowserClient实例
        profile_data: 配置文件数据
        available_tokens: 可用的token列表

    Returns:
        tuple: (是否成功, 提取到的邮箱, 是否使用了新token)
    """
    wallet_address = profile_data['wallet_address']

    # 1. 查找配置文件
    profiles, error_code, error_message = get_profile_list(wallet_address)

    if not profiles:
        print(f"❌ 未找到配置文件: {wallet_address}")
        return False, None, False

    profile_id = profiles[0].get('profile_id')
    print(f"✅ 找到配置文件: {wallet_address} (ID: {profile_id})")

    # 直接从profile列表中获取备注信息
    remark = profiles[0].get('note', '')
    print(f"📝 从profile列表获取备注信息: {len(remark)} 字符")

    if remark:
        print(f"📝 备注内容: {remark[:100]}{'...' if len(remark) > 100 else ''}")
        backup_email = extract_backup_email_from_remark(remark)
        backup_email_password = extract_backup_email_password_from_remark(remark)
    else:
        print("⚠️ 备注信息为空")
        backup_email = None
        backup_email_password = None

    if not backup_email:
        print("⚠️ 未找到备用邮箱，可能影响后续验证流程")
    if not backup_email_password:
        print("⚠️ 未找到备用邮箱密码，可能无法收取验证码")

    # 2. 打开浏览器
    print(f"🚀 正在打开浏览器...")
    try:
        open_result = client.open_profile(profile_id, load_profile_info_page=False)

        if not open_result:
            print(f"❌ 打开浏览器失败: {client.code} - {client.message}")
            print(f"⏭️ 跳过此浏览器，继续处理下一个")
            log_email_result(wallet_address, None, False, f"浏览器打开失败: {client.code} - {client.message}")
            return False, None, False

        print(f"✅ 浏览器打开成功")

    except Exception as e:
        print(f"❌ 打开浏览器时出错: {str(e)}")
        print(f"⏭️ 跳过此浏览器，继续处理下一个")
        log_email_result(wallet_address, None, False, f"浏览器打开异常: {str(e)}")
        return False, None, False

    # 3. 获取调试地址并连接Selenium
    debug_address = open_result.get('debugging_address', '')
    if debug_address.startswith('http://'):
        debug_address = debug_address[7:]
    elif debug_address.startswith('https://'):
        debug_address = debug_address[8:]

    chrome_options = Options()
    chrome_options.debugger_address = debug_address

    driver = None
    try:
        # 初始化WebDriver
        webdriver_path = open_result.get('webdriver', '')
        if webdriver_path:
            service = webdriver.chrome.service.Service(executable_path=webdriver_path)
            driver = webdriver.Chrome(service=service, options=chrome_options)
        else:
            driver = webdriver.Chrome(options=chrome_options)

        print("✅ WebDriver连接成功")

        # 4. 首先尝试直接访问邮箱设置页面
        print("📧 尝试直接访问邮箱设置页面...")
        extracted_email = extract_email_from_settings(driver)

        if extracted_email:
            print(f"✅ 成功提取邮箱: {extracted_email}")

            # 检查当前邮箱是否与备用邮箱相同
            if backup_email and extracted_email.lower() == backup_email.lower():
                print(f"⚠️ 当前邮箱与备用邮箱相同: {extracted_email}")
                print(f"⏭️ 跳过此浏览器，无需更新邮箱")
                print(f"🔒 自动关闭浏览器...")

                # 自动关闭浏览器
                try:
                    close_result = client.close_profile(profile_id)
                    if close_result:
                        print("✅ 浏览器已关闭")
                    else:
                        print("⚠️ 浏览器关闭可能失败")
                except Exception as e:
                    print(f"⚠️ 关闭浏览器时出错: {str(e)}")

                log_email_result(wallet_address, extracted_email, False, "当前邮箱与备用邮箱相同")
                return False, extracted_email, False

            # 查询数据库获取Twitter密码
            twitter_password = query_email_password(extracted_email)

            if not twitter_password:
                print(f"❌ 数据库中未找到邮箱 {extracted_email} 对应的密码")
                print(f"🔍 尝试通过用户名查询密码...")

                # 尝试提取用户名
                username = extract_twitter_username(driver)

                if username:
                    print(f"✅ 成功提取用户名: {username}")
                    # 根据用户名查询密码
                    twitter_password = query_username_password(username)

                    if twitter_password:
                        print(f"✅ 通过用户名找到Twitter密码: {twitter_password[:3]}***{twitter_password[-3:] if len(twitter_password) > 6 else '***'}")
                        print(f"🔄 返回邮箱设置页面继续邮箱更新流程...")

                        # 返回邮箱设置页面（有更新按钮的页面）
                        driver.get("https://x.com/settings/email")
                        print(f"⏳ 等待邮箱设置页面完全加载...")
                        time.sleep(15)  # 增加等待时间，确保页面完全加载

                        print(f"✅ 已返回邮箱设置页面，准备开始邮箱更新流程")
                        # 继续执行后续的邮箱更新逻辑（不要return，让代码继续执行）
                    else:
                        print(f"❌ 根据用户名也未找到对应的密码")
                        print(f"⏭️ 跳过此浏览器，继续处理下一个")
                        print(f"🔒 自动关闭浏览器...")

                        # 自动关闭浏览器
                        try:
                            close_result = client.close_profile(profile_id)
                            if close_result:
                                print("✅ 浏览器已关闭")
                            else:
                                print("⚠️ 浏览器关闭可能失败")
                        except Exception as e:
                            print(f"⚠️ 关闭浏览器时出错: {str(e)}")

                        log_email_result(wallet_address, extracted_email, False, "数据库中无对应密码（邮箱和用户名都查不到）")
                        return False, extracted_email, False
                else:
                    print(f"❌ 无法提取用户名")
                    print(f"⏭️ 跳过此浏览器，继续处理下一个")
                    print(f"🔒 自动关闭浏览器...")

                    # 自动关闭浏览器
                    try:
                        close_result = client.close_profile(profile_id)
                        if close_result:
                            print("✅ 浏览器已关闭")
                        else:
                            print("⚠️ 浏览器关闭可能失败")
                    except Exception as e:
                        print(f"⚠️ 关闭浏览器时出错: {str(e)}")

                    log_email_result(wallet_address, extracted_email, False, "数据库中无对应密码且无法提取用户名")
                    return False, extracted_email, False

            # 如果找到了密码（无论是通过邮箱还是用户名）
            if not twitter_password:
                # 这种情况不应该发生，但为了安全起见
                print(f"❌ 未找到Twitter密码")
                print(f"⏭️ 跳过此浏览器，继续处理下一个")
                print(f"🔒 自动关闭浏览器...")

                # 自动关闭浏览器
                try:
                    close_result = client.close_profile(profile_id)
                    if close_result:
                        print("✅ 浏览器已关闭")
                    else:
                        print("⚠️ 浏览器关闭可能失败")
                except Exception as e:
                    print(f"⚠️ 关闭浏览器时出错: {str(e)}")

                log_email_result(wallet_address, extracted_email, False, "未找到Twitter密码")
                return False, extracted_email, False

            print(f"✅ 找到Twitter密码: {twitter_password[:3]}***{twitter_password[-3:] if len(twitter_password) > 6 else '***'}")

            if backup_email:
                print(f"📧 备用邮箱: {backup_email}")
                print(f"🔄 将从 {extracted_email} 更新到 {backup_email}")

            log_email_result(wallet_address, extracted_email, True)

            # 点击更新邮箱按钮并完成完整验证流程
            if click_update_email_button(driver, extracted_email, backup_email, backup_email_password, twitter_password):
                print("✅ 邮箱更新流程已完成")
                print("🔒 自动关闭浏览器...")
                time.sleep(2)  # 等待2秒让用户看到结果

                # 关闭浏览器
                try:
                    close_result = client.close_profile(profile_id)
                    if close_result:
                        print("✅ 浏览器已关闭")
                    else:
                        print("⚠️ 浏览器关闭可能失败")
                except Exception as e:
                    print(f"⚠️ 关闭浏览器时出错: {str(e)}")
            else:
                print("❌ 邮箱更新流程失败")
                print("🔒 自动关闭浏览器...")
                time.sleep(2)

                # 关闭浏览器
                try:
                    close_result = client.close_profile(profile_id)
                    if close_result:
                        print("✅ 浏览器已关闭")
                    else:
                        print("⚠️ 浏览器关闭可能失败")
                except Exception as e:
                    print(f"⚠️ 关闭浏览器时出错: {str(e)}")

            return True, extracted_email, False

        # 5. 如果直接访问失败，直接跳过此浏览器
        print("❌ 直接访问失败，可能需要登录")
        print("⏭️ 跳过此浏览器，不尝试登录新token")
        print("🔒 自动关闭浏览器...")

        # 自动关闭浏览器
        try:
            close_result = client.close_profile(profile_id)
            if close_result:
                print("✅ 浏览器已关闭")
            else:
                print("⚠️ 浏览器关闭可能失败")
        except Exception as e:
            print(f"⚠️ 关闭浏览器时出错: {str(e)}")

        log_email_result(wallet_address, None, False, "token失效未登录")
        return False, None, False

    except Exception as e:
        print(f"❌ 处理过程中出错: {str(e)}")
        print(f"🔒 自动关闭浏览器...")

        # 自动关闭浏览器
        try:
            close_result = client.close_profile(profile_id)
            if close_result:
                print("✅ 浏览器已关闭")
            else:
                print("⚠️ 浏览器关闭可能失败")
        except Exception as close_e:
            print(f"⚠️ 关闭浏览器时出错: {str(close_e)}")

        log_email_result(wallet_address, None, False)
        return False, None, False


def main():
    """主函数"""
    print("📧 Twitter邮箱提取自动化脚本启动")
    print("=" * 50)

    # 配置文件路径
    accounts_file = 'examples/accounts.txt'
    tokens_file = 'examples/twitter.txt'

    # 初始化客户端
    client = IXBrowserClient()

    # 读取账户数据
    accounts_data = read_data_file(accounts_file)
    if not accounts_data:
        print("❌ 没有读取到有效的账户数据")
        return

    # 读取可用的Twitter tokens
    available_tokens = read_twitter_tokens(tokens_file)
    if not available_tokens:
        print("❌ 没有读取到可用的Twitter tokens")
        return

    print(f"📋 共找到 {len(accounts_data)} 个账户")
    print(f"🎯 共有 {len(available_tokens)} 个可用token")

    # 处理统计
    success_count = 0
    failed_count = 0
    token_used_count = 0

    # 处理每个账户
    for i, data in enumerate(accounts_data):
        wallet_address = data['wallet_address']
        print(f"\n{'='*20} 处理第 {i+1}/{len(accounts_data)} 个账户 {'='*20}")
        print(f"钱包地址: {wallet_address}")
        print(f"当前token: {data['twitter_token'][:20]}...")

        try:
            success, extracted_email, used_new_token = process_profile(
                client, data, available_tokens
            )

            if success:
                success_count += 1
                if used_new_token:
                    token_used_count += 1
                    print(f"✅ 账户 {wallet_address} 处理成功，已使用新token")
                else:
                    print(f"✅ 账户 {wallet_address} 处理成功，使用现有登录状态")

                if extracted_email:
                    print(f"📧 提取到邮箱: {extracted_email}")
            else:
                failed_count += 1
                print(f"❌ 账户 {wallet_address} 处理失败")
                handle_failed_profile(data, 'failed_email_extraction.txt')

        except Exception as e:
            failed_count += 1
            print(f"❌ 处理账户 {wallet_address} 时发生异常: {str(e)}")
            handle_failed_profile(data, 'failed_email_extraction.txt')

        # 如果没有可用token了，提前结束
        if not available_tokens:
            print("⚠️ 没有更多可用token，停止处理")
            break

        print(f"📊 剩余可用token: {len(available_tokens)} 个")

        # 在处理完当前账户后等待30秒
        if i < len(accounts_data) - 1:  # 不是最后一个账户
            print(f"\n⏳ 等待30秒后处理下一个账户...")
            time.sleep(55)

            print(f"\n🌐 准备为下一个账户切换IP...")
            ip_switch_success = switch_ip()
            if not ip_switch_success:
                print("⚠️ IP切换失败，但继续处理下一个账户...")

    # 输出统计结果
    print(f"\n{'='*50}")
    print(f"📊 处理完成统计:")
    print(f"   ✅ 成功: {success_count} 个")
    print(f"   ❌ 失败: {failed_count} 个")
    print(f"   🔄 使用新token: {token_used_count} 个")
    print(f"   🎯 剩余可用token: {len(available_tokens)} 个")
    print(f"   📝 提取日志: email_extraction.log")
    print(f"   📝 失败记录: failed_email_extraction.txt")
    print("🎉 脚本执行完成！")


if __name__ == '__main__':
    main()
