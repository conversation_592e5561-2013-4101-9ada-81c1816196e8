#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Twitter 个人资料更新脚本
基于 twitter_email_extractor.py 修改
功能：批量修改推特个人资料，在名字后面添加 $GARA army，保存并截图
"""

import os
import sys
import time
import json
import httpx
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from ixbrowser_local_api import IXBrowserClient


def switch_ip():
    """切换IP地址"""
    try:
        print("🔄 正在切换IP地址...")
        
        # 调用IP切换API
        with httpx.Client(timeout=30.0) as client:
            response = client.get("http://localhost:8888/switch")
            
            if response.status_code == 200:
                print("✅ IP切换成功")
                # 等待IP切换生效
                time.sleep(10)
                return True
            else:
                print(f"❌ IP切换失败: HTTP {response.status_code}")
                return False
                
    except Exception as e:
        print(f"❌ IP切换时出错: {str(e)}")
        return False


def get_profile_list(name=None, timeout=20.0):
    """获取配置文件列表"""
    client = None
    try:
        client = httpx.Client(timeout=timeout)
        url = "http://localhost:53200/api/v2/profile-list"
        params = {"page": 1, "limit": 100}
        
        if name:
            params["name"] = name
        
        response = client.post(url, json=params)
        data = response.json()
        
        if response.status_code == 200 and data.get("error", {}).get("code") == 0:
            profiles = data.get("data", {}).get("data", [])
            return profiles, None, None
        
        error_code = data.get("error", {}).get("code")
        error_message = data.get("error", {}).get("message")
        return None, error_code, error_message
        
    except Exception as e:
        return None, None, str(e)
    finally:
        if client:
            client.close()


def find_profile_by_wallet_address(wallet_address):
    """根据钱包地址查找对应的浏览器配置文件"""
    try:
        print(f"🔍 查找钱包地址对应的浏览器配置: {wallet_address}")

        # 使用钱包地址作为搜索参数
        profiles, error_code, error_message = get_profile_list(wallet_address)

        if not profiles:
            print(f"❌ 未找到配置文件: {wallet_address}")
            return None

        profile_id = profiles[0].get('profile_id')
        print(f"✅ 找到配置文件: {wallet_address} (ID: {profile_id})")

        return profile_id

    except Exception as e:
        print(f"❌ 查找配置文件时出错: {str(e)}")
        return None


def open_browser_by_profile_id(profile_id, client):
    """通过配置文件ID打开浏览器"""
    try:
        print(f"🚀 正在打开浏览器...")
        
        # 打开浏览器
        open_result = client.open_profile(profile_id, load_profile_info_page=False)

        if not open_result:
            print(f"❌ 打开浏览器失败: {client.code} - {client.message}")
            return None

        print(f"✅ 浏览器打开成功")

        # 获取调试地址并连接Selenium
        debug_address = open_result.get('debugging_address', '')
        if debug_address.startswith('http://'):
            debug_address = debug_address[7:]
        elif debug_address.startswith('https://'):
            debug_address = debug_address[8:]

        chrome_options = Options()
        chrome_options.debugger_address = debug_address

        driver = None
        try:
            # 初始化WebDriver
            webdriver_path = open_result.get('webdriver', '')
            if webdriver_path:
                service = webdriver.chrome.service.Service(executable_path=webdriver_path)
                driver = webdriver.Chrome(service=service, options=chrome_options)
            else:
                driver = webdriver.Chrome(options=chrome_options)

            # 设置超时时间
            driver.set_page_load_timeout(180)  # 页面加载超时3分钟
            driver.implicitly_wait(30)  # 隐式等待30秒

            print("✅ WebDriver连接成功")
            return driver

        except Exception as e:
            print(f"❌ WebDriver连接失败: {str(e)}")
            return None
            
    except Exception as e:
        print(f"❌ 打开浏览器时出错: {str(e)}")
        return None


def close_browser_by_profile_id(profile_id, client):
    """通过配置文件ID关闭浏览器"""
    try:
        print(f"🔒 正在关闭浏览器配置文件: {profile_id}")
        
        result = client.close_profile(profile_id)
        
        if result:
            print(f"✅ 浏览器关闭成功")
            return True
        else:
            print(f"❌ 浏览器关闭失败: {client.code} - {client.message}")
            return False
            
    except Exception as e:
        print(f"❌ 关闭浏览器时出错: {str(e)}")
        return False


def check_twitter_accessibility(driver):
    """检查推特是否可访问（未被封禁）"""
    try:
        print("🔍 检查推特是否可访问...")

        # 设置页面加载超时
        driver.set_page_load_timeout(60)

        try:
            # 访问推特主页
            driver.get("https://x.com")
            time.sleep(10)
        except Exception as e:
            print(f"❌ 推特页面加载超时或失败: {str(e)}")
            return False

        current_url = driver.current_url.lower()
        page_title = driver.title.lower()

        print(f"📍 当前URL: {current_url}")
        print(f"📄 页面标题: {page_title}")

        # 检查是否被封禁或无法访问
        blocked_indicators = [
            "suspended",
            "account suspended",
            "this account has been suspended",
            "blocked",
            "restricted",
            "unavailable",
            "error",
            "something went wrong"
        ]

        page_source = driver.page_source.lower()

        for indicator in blocked_indicators:
            if indicator in page_source or indicator in page_title:
                print(f"❌ 检测到账户被封禁或限制: {indicator}")
                return False

        # 检查是否成功加载到推特页面
        if "x.com" not in current_url and "twitter.com" not in current_url:
            print("❌ 未能访问到推特页面")
            return False

        print("✅ 推特页面可正常访问")
        return True

    except Exception as e:
        print(f"❌ 检查推特可访问性时出错: {str(e)}")
        return False


def check_twitter_login_status(driver):
    """检查推特登录状态"""
    try:
        print("🔍 检查推特登录状态...")

        current_url = driver.current_url.lower()

        # 检查是否被重定向到登录页面
        if "login" in current_url or "signin" in current_url:
            print("❌ 检测到未登录状态")
            return False

        # 检查是否有登录用户的特征元素
        try:
            # 查找用户头像或导航菜单
            user_elements = [
                '[data-testid="SideNav_AccountSwitcher_Button"]',
                '[data-testid="AppTabBar_Profile_Link"]',
                '[aria-label="Profile"]',
                '[data-testid="primaryColumn"]'
            ]

            for selector in user_elements:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                if elements and elements[0].is_displayed():
                    print("✅ 检测到已登录状态")
                    return True

        except Exception as e:
            print(f"⚠️ 检查登录元素时出错: {str(e)}")

        print("❌ 无法确定登录状态，假设未登录")
        return False

    except Exception as e:
        print(f"❌ 检查推特登录状态时出错: {str(e)}")
        return False


def update_twitter_profile(driver, wallet_address):
    """更新推特个人资料"""
    try:
        print("🔧 开始更新推特个人资料...")
        
        # 访问个人资料编辑页面
        print("📍 访问个人资料编辑页面...")
        driver.get("https://x.com/settings/profile")
        time.sleep(8)
        
        # 查找名字输入框
        print("🔍 查找名字输入框...")
        name_selectors = [
            '[data-testid="displayNameTextInput"]',
            'input[name="displayName"]',
            'input[placeholder*="Name"]',
            'input[placeholder*="name"]',
            'input[aria-label*="Name"]',
            'input[aria-label*="name"]'
        ]
        
        name_input = None
        for selector in name_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        name_input = element
                        print(f"✅ 找到名字输入框: {selector}")
                        break
                if name_input:
                    break
            except:
                continue
        
        if not name_input:
            print("❌ 未找到名字输入框")
            return False
        
        # 获取当前名字
        current_name = name_input.get_attribute('value') or ''
        print(f"📝 当前名字: '{current_name}'")
        
        # 检查是否已经包含 $GARA army
        if "$GARA army" in current_name:
            print("✅ 名字已经包含 $GARA army，无需修改")
            return True
        
        # 修改名字，添加 $GARA army
        new_name = current_name + " $GARA army"
        print(f"📝 新名字: '{new_name}'")
        
        # 清空输入框并输入新名字
        name_input.clear()
        time.sleep(1)
        name_input.send_keys(new_name)
        time.sleep(2)
        
        # 查找并点击保存按钮
        print("🔍 查找保存按钮...")
        save_selectors = [
            '[data-testid="Profile_Save_Button"]',
            'button[type="submit"]',
            'button:contains("Save")',
            'button:contains("保存")',
            '[role="button"]:contains("Save")',
            '[role="button"]:contains("保存")'
        ]
        
        save_button = None
        for selector in save_selectors:
            try:
                if ':contains(' in selector:
                    # 使用XPath处理包含文本的选择器
                    xpath_selector = f"//button[contains(text(), 'Save')] | //button[contains(text(), '保存')]"
                    elements = driver.find_elements(By.XPATH, xpath_selector)
                else:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                
                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        save_button = element
                        print(f"✅ 找到保存按钮: {selector}")
                        break
                if save_button:
                    break
            except:
                continue
        
        if not save_button:
            print("❌ 未找到保存按钮")
            return False
        
        # 点击保存按钮
        print("💾 点击保存按钮...")
        save_button.click()
        time.sleep(5)
        
        print("✅ 个人资料更新完成")
        return True
        
    except Exception as e:
        print(f"❌ 更新推特个人资料时出错: {str(e)}")
        return False


def take_screenshot(driver, wallet_address):
    """截图并保存"""
    try:
        print("📸 正在截图...")
        
        # 确保截图目录存在
        screenshot_dir = "screenshots"
        if not os.path.exists(screenshot_dir):
            os.makedirs(screenshot_dir)
        
        # 以钱包地址命名截图文件
        screenshot_filename = f"{wallet_address}.png"
        screenshot_path = os.path.join(screenshot_dir, screenshot_filename)
        
        # 截图
        driver.save_screenshot(screenshot_path)
        
        print(f"✅ 截图已保存: {screenshot_path}")
        return True
        
    except Exception as e:
        print(f"❌ 截图时出错: {str(e)}")
        return False


def process_twitter_profile_update(profile_data, client):
    """处理单个账户的推特个人资料更新"""
    wallet_address = profile_data['wallet_address']
    profile_id = None
    driver = None

    try:
        print(f"\n{'='*60}")
        print(f"🚀 开始处理钱包地址: {wallet_address}")
        print(f"{'='*60}")

        # 1. 切换IP地址（每次新任务前必须切换）
        print("🌐 切换IP地址（新任务开始前）...")
        ip_switch_success = switch_ip()
        if not ip_switch_success:
            print("❌ IP切换失败，跳过该账户")
            return False

        # 2. 查找对应的浏览器配置文件
        profile_id = find_profile_by_wallet_address(wallet_address)
        if not profile_id:
            print(f"❌ 未找到钱包地址 {wallet_address} 对应的浏览器配置文件，跳过")
            return False

        # 3. 打开浏览器
        driver = open_browser_by_profile_id(profile_id, client)
        if not driver:
            print(f"❌ 浏览器打开失败，跳过该账户")
            return False

        # 4. 等待浏览器完全启动
        print("⏳ 等待浏览器完全启动...")
        time.sleep(8)

        # 5. 检查推特是否可访问（未被封禁）
        if not check_twitter_accessibility(driver):
            print(f"❌ 推特无法访问或被封禁，跳过该账户")
            return False

        # 6. 检查推特登录状态
        if not check_twitter_login_status(driver):
            print(f"❌ 推特未登录，跳过该账户")
            return False

        # 7. 更新推特个人资料
        if not update_twitter_profile(driver, wallet_address):
            print(f"❌ 更新推特个人资料失败")
            return False

        # 8. 截图保存
        if not take_screenshot(driver, wallet_address):
            print(f"❌ 截图失败")
            return False

        print(f"✅ 钱包地址 {wallet_address} 推特个人资料更新成功")

        # 任务完成后停留60秒
        print("⏳ 任务完成！停留60秒后关闭浏览器...")
        time.sleep(60)

        return True

    except Exception as e:
        print(f"❌ 处理账户 {wallet_address} 时出错: {str(e)}")
        return False

    finally:
        # 确保每次任务结束都关闭浏览器
        print("🔒 正在关闭浏览器...")
        try:
            if driver:
                driver.quit()
                print("✅ WebDriver已关闭")
        except Exception as e:
            print(f"⚠️ 关闭WebDriver时出错: {str(e)}")

        try:
            if profile_id:
                close_browser_by_profile_id(profile_id, client)
                print("✅ 浏览器配置文件已关闭")
        except Exception as e:
            print(f"⚠️ 关闭浏览器配置文件时出错: {str(e)}")

        print("🔒 浏览器清理完成")


def read_data_file(file_path):
    """读取账户数据文件"""
    try:
        profiles_data = []
        
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:
                    continue
                    
                try:
                    profile_data = json.loads(line)
                    if 'wallet_address' in profile_data:
                        profiles_data.append(profile_data)
                    else:
                        print(f"⚠️ 第 {line_num} 行缺少 wallet_address 字段，跳过")
                        
                except json.JSONDecodeError as e:
                    print(f"⚠️ 第 {line_num} 行 JSON 格式错误: {str(e)}")
                    continue
        
        return profiles_data
        
    except Exception as e:
        print(f"❌ 读取数据文件时出错: {str(e)}")
        return []


def handle_failed_profile(profile_data, failed_file):
    """处理失败的账户数据"""
    try:
        with open(failed_file, 'a', encoding='utf-8') as f:
            f.write(json.dumps(profile_data, ensure_ascii=False) + '\n')
    except Exception as e:
        print(f"❌ 保存失败数据时出错: {str(e)}")


def main():
    """主函数"""
    try:
        print("🚀 开始 Twitter 个人资料更新脚本")
        print("=" * 60)
        
        # 读取账户数据文件
        data_file = input("请输入数据文件路径 (默认: accounts.txt): ").strip()
        if not data_file:
            data_file = "accounts.txt"
        
        if not os.path.exists(data_file):
            print(f"❌ 数据文件 {data_file} 不存在")
            return
        
        # 初始化客户端
        client = IXBrowserClient()
        
        # 读取账户数据
        profiles_data = read_data_file(data_file)
        print(f"📋 读取到 {len(profiles_data)} 个账户")
        
        if not profiles_data:
            print("❌ 没有读取到有效的账户数据")
            return
        
        # 处理每个账户
        success_count = 0
        failed_count = 0
        
        for i, profile_data in enumerate(profiles_data, 1):
            try:
                print(f"\n🔄 处理第 {i}/{len(profiles_data)} 个账户")

                # 处理推特个人资料更新
                if process_twitter_profile_update(profile_data, client):
                    success_count += 1
                    print(f"✅ 第 {i} 个账户处理成功")
                else:
                    failed_count += 1
                    print(f"❌ 第 {i} 个账户处理失败")
                    # 将失败的账户保存到文件
                    handle_failed_profile(profile_data, 'failed_twitter_profile_update.txt')

                # 账户间等待10秒（因为每个任务完成后已经停留了60秒）
                if i < len(profiles_data):
                    print("⏳ 等待10秒后处理下一个账户...")
                    time.sleep(10)

            except Exception as e:
                failed_count += 1
                print(f"❌ 处理第 {i} 个账户时出错: {str(e)}")
                handle_failed_profile(profile_data, 'failed_twitter_profile_update.txt')

                # 即使出错也要确保浏览器被关闭
                print("🔒 确保浏览器资源清理...")
                time.sleep(5)
                continue
        
        # 输出统计结果
        print(f"\n{'='*60}")
        print(f"📊 处理完成统计:")
        print(f"✅ 成功: {success_count} 个账户")
        print(f"❌ 失败: {failed_count} 个账户")
        print(f"📋 总计: {len(profiles_data)} 个账户")
        print(f"{'='*60}")
        
        if failed_count > 0:
            print(f"📄 失败的账户已保存到: failed_twitter_profile_update.txt")
        
        print(f"📸 截图已保存到: screenshots/ 目录")
        
    except Exception as e:
        print(f"❌ 脚本执行时出错: {str(e)}")


if __name__ == "__main__":
    main()
