#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试 Zealy.io 登录按钮查找问题
"""

import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from ixbrowser_local_api import IXBrowserClient

def debug_zealy_login_button():
    """调试 Zealy.io 登录按钮"""
    client = IXBrowserClient()
    driver = None
    
    try:
        # 使用第一个账户的钱包地址进行测试
        wallet_address = "******************************************"
        
        print(f"🔍 查找钱包地址: {wallet_address}")
        
        # 获取配置文件列表
        import httpx
        with httpx.Client(timeout=20.0) as http_client:
            url = "http://localhost:53200/api/v2/profile-list"
            params = {"page": 1, "limit": 100}
            params["name"] = wallet_address
            
            response = http_client.post(url, json=params)
            data = response.json()
            
            if response.status_code == 200 and data.get("error", {}).get("code") == 0:
                profiles = data.get("data", {}).get("data", [])
                if profiles:
                    profile_id = profiles[0].get('profile_id')
                    print(f"✅ 找到配置文件 ID: {profile_id}")
                else:
                    print("❌ 未找到配置文件")
                    return
            else:
                print("❌ 获取配置文件失败")
                return
        
        # 打开浏览器
        print("🚀 正在打开浏览器...")
        open_result = client.open_profile(profile_id, load_profile_info_page=False)
        
        if not open_result:
            print(f"❌ 打开浏览器失败")
            return
        
        print("✅ 浏览器打开成功")
        
        # 连接 WebDriver
        debug_address = open_result.get('debugging_address', '')
        if debug_address.startswith('http://'):
            debug_address = debug_address[7:]
        elif debug_address.startswith('https://'):
            debug_address = debug_address[8:]
        
        chrome_options = Options()
        chrome_options.debugger_address = debug_address
        
        webdriver_path = open_result.get('webdriver', '')
        if webdriver_path:
            service = webdriver.chrome.service.Service(executable_path=webdriver_path)
            driver = webdriver.Chrome(service=service, options=chrome_options)
        else:
            driver = webdriver.Chrome(options=chrome_options)
        
        print("✅ WebDriver连接成功")
        
        # 访问登录页面
        print("🌐 访问 Zealy.io 登录页面...")
        driver.get("https://zealy.io/login")
        time.sleep(10)
        
        print(f"📍 当前URL: {driver.current_url}")
        print(f"📄 页面标题: {driver.title}")
        
        # 分析页面上的所有按钮
        print("\n🔍 分析页面上的所有按钮...")
        buttons = driver.find_elements(By.TAG_NAME, "button")
        print(f"📋 找到 {len(buttons)} 个按钮:")
        
        for i, btn in enumerate(buttons):
            try:
                if btn.is_displayed():
                    text = btn.text.strip()
                    classes = btn.get_attribute('class')
                    print(f"  {i+1:2d}. 按钮文本: '{text}' | 类名: {classes[:50]}...")
            except:
                continue
        
        # 分析所有链接
        print("\n🔍 分析页面上的所有链接...")
        links = driver.find_elements(By.TAG_NAME, "a")
        print(f"📋 找到 {len(links)} 个链接:")
        
        for i, link in enumerate(links[:10]):  # 只显示前10个
            try:
                if link.is_displayed():
                    text = link.text.strip()
                    href = link.get_attribute('href')
                    if text:
                        print(f"  {i+1:2d}. 链接文本: '{text}' | href: {href[:50]}...")
            except:
                continue
        
        # 分析所有div
        print("\n🔍 分析页面上包含wallet关键词的div...")
        divs = driver.find_elements(By.TAG_NAME, "div")
        wallet_divs = []
        
        for div in divs:
            try:
                if div.is_displayed():
                    text = div.text.strip()
                    if text and any(keyword in text.lower() for keyword in ['wallet', 'login', 'connect']):
                        wallet_divs.append(text)
            except:
                continue
        
        print(f"📋 找到 {len(wallet_divs)} 个包含钱包关键词的div:")
        for i, text in enumerate(wallet_divs[:10]):  # 只显示前10个
            print(f"  {i+1:2d}. '{text[:100]}...'")
        
        # 尝试查找特定的登录按钮
        print("\n🔍 尝试查找 'Log in with wallet' 按钮...")
        
        wallet_login_selectors = [
            "//button[contains(text(), 'Log in with wallet')]",
            "//button[contains(text(), 'Log in with Wallet')]",
            "//button[contains(text(), 'LOGIN WITH WALLET')]",
            "//button[contains(text(), 'Connect wallet')]",
            "//button[contains(text(), 'Connect Wallet')]",
            "//a[contains(text(), 'Log in with wallet')]",
            "//a[contains(text(), 'Log in with Wallet')]",
            "//div[contains(text(), 'Log in with wallet')]",
            "//div[contains(text(), 'Log in with Wallet')]"
        ]
        
        found_elements = []
        for selector in wallet_login_selectors:
            try:
                elements = driver.find_elements(By.XPATH, selector)
                for element in elements:
                    if element.is_displayed():
                        found_elements.append({
                            'selector': selector,
                            'text': element.text,
                            'tag': element.tag_name,
                            'element': element
                        })
            except Exception as e:
                print(f"⚠️ 选择器失败: {selector} - {str(e)}")
        
        if found_elements:
            print(f"✅ 找到 {len(found_elements)} 个匹配的元素:")
            for i, elem in enumerate(found_elements, 1):
                print(f"  {i}. 选择器: {elem['selector']}")
                print(f"     文本: '{elem['text']}'")
                print(f"     标签: {elem['tag']}")
                print("-" * 50)
        else:
            print("❌ 未找到任何匹配的登录按钮")
        
        # 等待用户输入
        input("\n按回车键关闭浏览器...")
        
    except Exception as e:
        print(f"❌ 调试过程中出错: {str(e)}")
        import traceback
        print(f"🔍 详细错误信息: {traceback.format_exc()}")
    
    finally:
        # 清理资源
        if driver:
            try:
                driver.quit()
            except:
                pass
        
        try:
            client.close_profile(profile_id)
        except:
            pass

if __name__ == "__main__":
    debug_zealy_login_button()
