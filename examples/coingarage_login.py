#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Coingarage 登录访问脚本
基于 twitter_email_extractor.py 修改
功能：批量访问 https://trade.coingarage.io/login 页面，每个账户访问前更换IP
"""

import os
import sys
import time
import json
import httpx
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from ixbrowser_local_api import IXBrowserClient


def switch_ip():
    """切换IP地址"""
    try:
        print("🔄 正在切换IP地址...")
        
        # 调用IP切换API
        with httpx.Client(timeout=30.0) as client:
            response = client.get("http://localhost:8888/switch")
            
            if response.status_code == 200:
                print("✅ IP切换成功")
                # 等待IP切换生效
                time.sleep(10)
                return True
            else:
                print(f"❌ IP切换失败: HTTP {response.status_code}")
                return False
                
    except Exception as e:
        print(f"❌ IP切换时出错: {str(e)}")
        return False


def get_profile_list(name=None, timeout=20.0):
    """获取配置文件列表"""
    client = None
    try:
        client = httpx.Client(timeout=timeout)
        url = "http://localhost:53200/api/v2/profile-list"
        params = {"page": 1, "limit": 100}
        
        if name:
            params["name"] = name
        
        response = client.post(url, json=params)
        data = response.json()
        
        if response.status_code == 200 and data.get("error", {}).get("code") == 0:
            profiles = data.get("data", {}).get("data", [])
            return profiles, None, None
        
        error_code = data.get("error", {}).get("code")
        error_message = data.get("error", {}).get("message")
        return None, error_code, error_message
        
    except Exception as e:
        return None, None, str(e)
    finally:
        if client:
            client.close()


def find_profile_by_wallet_address(wallet_address):
    """根据钱包地址查找对应的浏览器配置文件"""
    try:
        print(f"🔍 查找钱包地址对应的浏览器配置: {wallet_address}")

        # 使用钱包地址作为搜索参数
        profiles, error_code, error_message = get_profile_list(wallet_address)

        if not profiles:
            print(f"❌ 未找到配置文件: {wallet_address}")
            return None

        profile_id = profiles[0].get('profile_id')
        print(f"✅ 找到配置文件: {wallet_address} (ID: {profile_id})")

        return profile_id

    except Exception as e:
        print(f"❌ 查找配置文件时出错: {str(e)}")
        return None


def open_browser_by_profile_id(profile_id, client):
    """通过配置文件ID打开浏览器"""
    try:
        print(f"🚀 正在打开浏览器...")
        
        # 打开浏览器
        open_result = client.open_profile(profile_id, load_profile_info_page=False)

        if not open_result:
            print(f"❌ 打开浏览器失败: {client.code} - {client.message}")
            return None

        print(f"✅ 浏览器打开成功")

        # 获取调试地址并连接Selenium
        debug_address = open_result.get('debugging_address', '')
        if debug_address.startswith('http://'):
            debug_address = debug_address[7:]
        elif debug_address.startswith('https://'):
            debug_address = debug_address[8:]

        chrome_options = Options()
        chrome_options.debugger_address = debug_address

        driver = None
        try:
            # 初始化WebDriver
            webdriver_path = open_result.get('webdriver', '')
            if webdriver_path:
                service = webdriver.chrome.service.Service(executable_path=webdriver_path)
                driver = webdriver.Chrome(service=service, options=chrome_options)
            else:
                driver = webdriver.Chrome(options=chrome_options)

            # 设置超时时间
            driver.set_page_load_timeout(180)  # 页面加载超时3分钟
            driver.implicitly_wait(30)  # 隐式等待30秒

            print("✅ WebDriver连接成功")
            return driver

        except Exception as e:
            print(f"❌ WebDriver连接失败: {str(e)}")
            return None
            
    except Exception as e:
        print(f"❌ 打开浏览器时出错: {str(e)}")
        return None


def close_browser_by_profile_id(profile_id, client):
    """通过配置文件ID关闭浏览器"""
    try:
        print(f"🔒 正在关闭浏览器配置文件: {profile_id}")
        
        result = client.close_profile(profile_id)
        
        if result:
            print(f"✅ 浏览器关闭成功")
            return True
        else:
            print(f"❌ 浏览器关闭失败: {client.code} - {client.message}")
            return False
            
    except Exception as e:
        print(f"❌ 关闭浏览器时出错: {str(e)}")
        return False


def visit_coingarage_login(driver):
    """访问 Coingarage 登录页面"""
    try:
        print("🌐 正在访问 Coingarage 登录页面...")
        
        # 访问登录页面
        target_url = "https://trade.coingarage.io/login"
        print(f"📍 开始访问: {target_url}")
        
        driver.get(target_url)
        
        # 等待页面加载
        print("⏳ 等待页面完全加载...")
        time.sleep(10)
        
        # 检查页面是否正确加载
        current_url = driver.current_url
        page_title = driver.title
        
        print(f"📍 访问后的URL: {current_url}")
        print(f"📄 页面标题: {page_title}")
        
        # 验证是否成功访问到目标页面
        if "coingarage" in current_url.lower():
            print("✅ 成功访问 Coingarage 登录页面")
            return True
        else:
            print(f"❌ 页面未正确加载到 Coingarage 网站")
            return False
            
    except Exception as e:
        print(f"❌ 访问 Coingarage 登录页面时出错: {str(e)}")
        return False


def process_coingarage_visit(profile_data, client):
    """处理单个账户的 Coingarage 访问"""
    wallet_address = profile_data['wallet_address']
    profile_id = None
    driver = None
    
    try:
        print(f"\n{'='*60}")
        print(f"🚀 开始处理钱包地址: {wallet_address}")
        print(f"{'='*60}")
        
        # 1. 切换IP地址
        print("🌐 准备切换IP地址...")
        ip_switch_success = switch_ip()
        if not ip_switch_success:
            print("⚠️ IP切换失败，但继续处理...")

        # 2. 查找对应的浏览器配置文件
        profile_id = find_profile_by_wallet_address(wallet_address)
        if not profile_id:
            print(f"❌ 未找到钱包地址 {wallet_address} 对应的浏览器配置文件，跳过")
            return False

        # 3. 打开浏览器
        driver = open_browser_by_profile_id(profile_id, client)
        if not driver:
            print(f"❌ 浏览器打开失败，跳过该账户")
            return False

        # 4. 等待浏览器完全启动
        print("⏳ 等待浏览器完全启动...")
        time.sleep(5)

        # 5. 访问 Coingarage 登录页面
        if not visit_coingarage_login(driver):
            print(f"❌ 访问 Coingarage 登录页面失败")
            return False

        print(f"✅ 钱包地址 {wallet_address} 成功访问 Coingarage 登录页面")
        
        # 停留一段时间观察页面
        print("⏳ 停留10秒观察页面...")
        time.sleep(10)
        
        return True
        
    except Exception as e:
        print(f"❌ 处理账户 {wallet_address} 时出错: {str(e)}")
        return False
        
    finally:
        # 清理资源
        try:
            if driver:
                driver.quit()
                print("🔒 浏览器已关闭")
        except:
            pass
            
        try:
            if profile_id:
                close_browser_by_profile_id(profile_id, client)
        except:
            pass


def read_data_file(file_path):
    """读取账户数据文件"""
    try:
        profiles_data = []
        
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:
                    continue
                    
                try:
                    profile_data = json.loads(line)
                    if 'wallet_address' in profile_data:
                        profiles_data.append(profile_data)
                    else:
                        print(f"⚠️ 第 {line_num} 行缺少 wallet_address 字段，跳过")
                        
                except json.JSONDecodeError as e:
                    print(f"⚠️ 第 {line_num} 行 JSON 格式错误: {str(e)}")
                    continue
        
        return profiles_data
        
    except Exception as e:
        print(f"❌ 读取数据文件时出错: {str(e)}")
        return []


def handle_failed_profile(profile_data, failed_file):
    """处理失败的账户数据"""
    try:
        with open(failed_file, 'a', encoding='utf-8') as f:
            f.write(json.dumps(profile_data, ensure_ascii=False) + '\n')
    except Exception as e:
        print(f"❌ 保存失败数据时出错: {str(e)}")


def main():
    """主函数"""
    try:
        print("🚀 开始 Coingarage 登录页面访问脚本")
        print("=" * 60)
        
        # 读取账户数据文件
        data_file = input("请输入数据文件路径 (默认: accounts.txt): ").strip()
        if not data_file:
            data_file = "accounts.txt"
        
        if not os.path.exists(data_file):
            print(f"❌ 数据文件 {data_file} 不存在")
            return
        
        # 初始化客户端
        client = IXBrowserClient()
        
        # 读取账户数据
        profiles_data = read_data_file(data_file)
        print(f"📋 读取到 {len(profiles_data)} 个账户")
        
        if not profiles_data:
            print("❌ 没有读取到有效的账户数据")
            return
        
        # 处理每个账户
        success_count = 0
        failed_count = 0
        
        for i, profile_data in enumerate(profiles_data, 1):
            try:
                print(f"\n🔄 处理第 {i}/{len(profiles_data)} 个账户")

                # 处理访问
                if process_coingarage_visit(profile_data, client):
                    success_count += 1
                    print(f"✅ 第 {i} 个账户处理成功")
                else:
                    failed_count += 1
                    print(f"❌ 第 {i} 个账户处理失败")
                    # 将失败的账户保存到文件
                    handle_failed_profile(profile_data, 'failed_coingarage_visit.txt')

                # 账户间等待30秒
                if i < len(profiles_data):
                    print("⏳ 等待30秒后处理下一个账户...")
                    time.sleep(30)
                
            except Exception as e:
                failed_count += 1
                print(f"❌ 处理第 {i} 个账户时出错: {str(e)}")
                handle_failed_profile(profile_data, 'failed_coingarage_visit.txt')
                continue
        
        # 输出统计结果
        print(f"\n{'='*60}")
        print(f"📊 处理完成统计:")
        print(f"✅ 成功: {success_count} 个账户")
        print(f"❌ 失败: {failed_count} 个账户")
        print(f"📋 总计: {len(profiles_data)} 个账户")
        print(f"{'='*60}")
        
        if failed_count > 0:
            print(f"📄 失败的账户已保存到: failed_coingarage_visit.txt")
        
    except Exception as e:
        print(f"❌ 脚本执行时出错: {str(e)}")


if __name__ == "__main__":
    main()
