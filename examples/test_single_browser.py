import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options

from ixbrowser_local_api import IXBrowserClient
from examples.twitter_email_extractor import (
    get_profile_list,
    get_profile_remark,
    extract_backup_email_from_remark,
    extract_backup_email_password_from_remark,
    wait_for_page_load,
    extract_email_from_settings,
    click_update_email_button,
    query_email_password,
    log_email_result
)


def test_single_browser():
    """测试单个浏览器的完整流程"""
    print("🧪 单个浏览器测试工具")
    print("=" * 50)
    
    # 获取用户输入的浏览器名字
    browser_name = input("请输入要测试的浏览器名字（钱包地址）: ").strip()
    
    if not browser_name:
        print("❌ 浏览器名字不能为空")
        return
    
    print(f"🔍 开始测试浏览器: {browser_name}")
    
    # 初始化客户端
    client = IXBrowserClient()
    
    # 1. 查找配置文件
    print(f"\n📋 步骤1: 查找配置文件")
    profiles, error_code, error_message = get_profile_list(browser_name)
    
    if not profiles:
        print(f"❌ 未找到配置文件: {browser_name}")
        if error_message:
            print(f"   错误信息: {error_message}")
        return
    
    profile_id = profiles[0].get('profile_id')
    profile_name = profiles[0].get('name', 'Unknown')
    print(f"✅ 找到配置文件:")
    print(f"   ID: {profile_id}")
    print(f"   名称: {profile_name}")

    # 2. 从profile列表中读取备注信息
    print(f"\n📝 步骤2: 从profile列表读取备注信息")
    remark = profiles[0].get('note', '')

    if remark:
        print(f"✅ 成功读取备注: {len(remark)} 字符")
        print(f"📝 备注内容: {remark[:100]}{'...' if len(remark) > 100 else ''}")

        # 提取备用邮箱信息
        backup_email = extract_backup_email_from_remark(remark)
        backup_email_password = extract_backup_email_password_from_remark(remark)

        if backup_email:
            print(f"📧 备用邮箱: {backup_email}")
        else:
            print("❌ 未找到备用邮箱")

        if backup_email_password:
            print(f"🔑 备用邮箱密码: {backup_email_password[:3]}***{backup_email_password[-3:] if len(backup_email_password) > 6 else '***'}")
        else:
            print("❌ 未找到备用邮箱密码")
    else:
        print("❌ 备注信息为空")
        backup_email = None
        backup_email_password = None
    
    # 3. 打开浏览器
    print(f"\n🚀 步骤3: 打开浏览器")
    try:
        open_result = client.open_profile(profile_id, load_profile_info_page=False)

        if not open_result:
            print(f"❌ 打开浏览器失败: {client.code} - {client.message}")
            print(f"⏭️ 在批量处理模式下，此浏览器会被自动跳过")
            log_email_result(browser_name, None, False, f"浏览器打开失败: {client.code} - {client.message}")
            return

        print(f"✅ 浏览器打开成功")

    except Exception as e:
        print(f"❌ 打开浏览器时出错: {str(e)}")
        print(f"⏭️ 在批量处理模式下，此浏览器会被自动跳过")
        log_email_result(browser_name, None, False, f"浏览器打开异常: {str(e)}")
        return
    
    debug_address = open_result.get('debugging_address', '')
    print(f"✅ 浏览器已打开")
    print(f"🔗 调试地址: {debug_address}")
    
    # 4. 连接Selenium
    print(f"\n🔗 步骤4: 连接Selenium")
    if debug_address.startswith('http://'):
        debug_address = debug_address[7:]
    elif debug_address.startswith('https://'):
        debug_address = debug_address[8:]
    
    chrome_options = Options()
    chrome_options.debugger_address = debug_address
    
    driver = None
    try:
        # 初始化WebDriver
        webdriver_path = open_result.get('webdriver', '')
        if webdriver_path:
            service = webdriver.chrome.service.Service(executable_path=webdriver_path)
            driver = webdriver.Chrome(service=service, options=chrome_options)
        else:
            driver = webdriver.Chrome(options=chrome_options)
        
        print("✅ WebDriver连接成功")
        
        # 5. 访问Twitter邮箱设置页面
        print(f"\n📧 步骤5: 访问Twitter邮箱设置页面")
        zealy_url = "https://x.com/settings/email"
        print(f"🌐 正在访问: {zealy_url}")
        
        try:
            driver.get(zealy_url)
            print("✅ 页面访问成功")
        except Exception as e:
            print(f"❌ 页面访问失败: {str(e)}")
            return
        
        # 6. 等待页面加载
        print(f"\n⏳ 步骤6: 等待页面加载")
        if wait_for_page_load(driver):
            print("✅ 页面加载完成")
        else:
            print("⚠️ 页面加载可能不完整，但继续测试")
        
        # 7. 提取当前邮箱地址
        print(f"\n📧 步骤7: 提取当前邮箱地址")
        extracted_email = extract_email_from_settings(driver)
        
        if extracted_email:
            print(f"✅ 成功提取邮箱: {extracted_email}")

            # 7.5. 检查当前邮箱是否与备用邮箱相同
            if backup_email and extracted_email.lower() == backup_email.lower():
                print(f"\n⚠️ 当前邮箱与备用邮箱相同: {extracted_email}")
                print(f"⏭️ 在批量处理模式下，此浏览器会被自动跳过")
                log_email_result(browser_name, extracted_email, False, "当前邮箱与备用邮箱相同")
                print("💡 无需更新邮箱，流程结束")
                return

            # 8. 查询MySQL数据库获取密码
            print(f"\n🔍 步骤8: 查询数据库获取密码")
            twitter_password = query_email_password(extracted_email)

            if twitter_password:
                print(f"✅ 找到Twitter密码: {twitter_password[:3]}***{twitter_password[-3:] if len(twitter_password) > 6 else '***'}")

                # 9. 执行完整的更新流程
                print(f"\n🎯 步骤9: 执行完整的邮箱更新流程")

                # 询问用户是否继续
                continue_choice = input("是否继续执行邮箱更新流程？(y/n): ").strip().lower()

                if continue_choice == 'y':
                    success = click_update_email_button(driver, extracted_email, backup_email, backup_email_password, twitter_password)

                    if success:
                        print("✅ 邮箱更新流程执行成功")
                        log_email_result(browser_name, extracted_email, True)
                    else:
                        print("❌ 邮箱更新流程执行失败")
                        log_email_result(browser_name, extracted_email, False)
                else:
                    print("⏭️ 跳过邮箱更新流程")

            else:
                print("❌ 数据库中未找到邮箱对应的Twitter密码")
                print("🔍 尝试通过用户名查询密码...")

                # 导入新的函数
                from examples.twitter_email_extractor import extract_twitter_username, query_username_password

                # 尝试提取用户名
                username = extract_twitter_username(driver)

                if username:
                    print(f"✅ 成功提取用户名: {username}")
                    # 根据用户名查询密码
                    twitter_password = query_username_password(username)

                    if twitter_password:
                        print(f"✅ 通过用户名找到Twitter密码: {twitter_password[:3]}***{twitter_password[-3:] if len(twitter_password) > 6 else '***'}")
                        print(f"🔄 返回邮箱设置页面继续邮箱更新流程...")

                        # 返回邮箱设置页面（有更新按钮的页面）
                        driver.get("https://x.com/settings/email")
                        print(f"⏳ 等待邮箱设置页面完全加载...")
                        time.sleep(15)  # 增加等待时间，确保页面完全加载

                        print(f"✅ 已返回邮箱设置页面")
                        print(f"📧 备用邮箱: {backup_email}")
                        print(f"🔄 将从 {extracted_email} 更新到 {backup_email}")

                        # 询问是否继续邮箱更新流程
                        continue_update = input("是否继续邮箱更新流程？(y/n): ").strip().lower()
                        if continue_update == 'y':
                            print("🔄 开始邮箱更新流程...")
                            if click_update_email_button(driver, extracted_email, backup_email, backup_email_password, twitter_password):
                                print("✅ 邮箱更新流程完成")
                                log_email_result(browser_name, extracted_email, True)
                            else:
                                print("❌ 邮箱更新流程失败")
                                log_email_result(browser_name, extracted_email, False)
                        else:
                            print("⏭️ 跳过邮箱更新流程")
                    else:
                        print("❌ 根据用户名也未找到对应的密码")
                        print("⏭️ 跳过此浏览器")
                        print("🔒 自动关闭浏览器...")

                        # 自动关闭浏览器
                        try:
                            close_result = client.close_profile(profile_id)
                            if close_result:
                                print("✅ 浏览器已关闭")
                            else:
                                print("⚠️ 浏览器关闭可能失败")
                        except Exception as e:
                            print(f"⚠️ 关闭浏览器时出错: {str(e)}")

                        log_email_result(browser_name, extracted_email, False, "数据库中无对应密码（邮箱和用户名都查不到）")
                        print("💡 在批量处理模式下，此浏览器会被自动跳过")
                        return
                else:
                    print("❌ 无法提取用户名")
                    print("⏭️ 跳过此浏览器")
                    print("🔒 自动关闭浏览器...")

                    # 自动关闭浏览器
                    try:
                        close_result = client.close_profile(profile_id)
                        if close_result:
                            print("✅ 浏览器已关闭")
                        else:
                            print("⚠️ 浏览器关闭可能失败")
                    except Exception as e:
                        print(f"⚠️ 关闭浏览器时出错: {str(e)}")

                    log_email_result(browser_name, extracted_email, False, "数据库中无对应密码且无法提取用户名")
                    print("💡 在批量处理模式下，此浏览器会被自动跳过")
                    return
                
        else:
            print("❌ 未能提取到邮箱地址")
            print("💡 可能需要先登录Twitter")
        
        # 10. 保持浏览器打开
        print(f"\n💡 测试完成")
        print("🔒 自动关闭浏览器...")
        time.sleep(3)  # 等待3秒让用户看到结果
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {str(e)}")
    finally:
        # 关闭浏览器
        if driver:
            try:
                print("🔒 正在关闭浏览器...")
                close_result = client.close_profile(profile_id)
                if close_result:
                    print("✅ 浏览器已关闭")
                else:
                    print(f"⚠️ API关闭失败，使用Selenium关闭")
                    driver.quit()
            except Exception as e:
                print(f"⚠️ 关闭浏览器时出错: {str(e)}")
                try:
                    driver.quit()
                except:
                    pass


def show_test_info():
    """显示测试信息"""
    print("💡 单个浏览器测试说明")
    print("=" * 30)
    print("🎯 测试内容:")
    print("   1. 查找指定的浏览器配置文件")
    print("   2. 读取浏览器备注信息")
    print("   3. 提取备用邮箱和密码")
    print("   4. 打开浏览器并连接Selenium")
    print("   5. 访问Twitter邮箱设置页面")
    print("   6. 提取当前邮箱地址")
    print("   7. 查询MySQL数据库获取密码")
    print("   8. 执行完整的邮箱更新流程")
    print("   9. 自动填写备用邮箱和验证码")
    print()
    print("📋 测试流程:")
    print("   - 输入浏览器名字（钱包地址）")
    print("   - 自动执行所有步骤")
    print("   - 在关键步骤询问是否继续")
    print("   - 保持浏览器打开供手动检查")
    print()
    print("⚠️ 注意事项:")
    print("   - 确保浏览器配置文件存在")
    print("   - 确保备注信息格式正确")
    print("   - 确保MySQL数据库可访问")
    print("   - 确保IMAP邮箱可连接")


if __name__ == '__main__':
    print("🚀 开始单个浏览器测试")
    print("=" * 60)
    
    try:
        # 显示测试信息
        show_test_info()
        
        # 运行测试
        test_single_browser()
        
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {str(e)}")
