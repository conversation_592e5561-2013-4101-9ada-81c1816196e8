import time
import re
import imaplib
import email.message
import email.parser
from email.header import decode_header


def test_imap_connection():
    """测试IMAP连接和邮件解析"""
    print("🧪 测试IMAP连接和邮件解析")
    print("=" * 50)
    
    # IMAP配置
    IMAP_CONFIG = {
        'host': 'imap.firstmail.ltd',
        'port': 993,
        'use_ssl': True
    }
    
    # 获取测试邮箱信息
    test_email = input("请输入测试邮箱地址: ").strip()
    if not test_email:
        print("❌ 邮箱地址不能为空")
        return
    
    test_password = input("请输入邮箱密码: ").strip()
    if not test_password:
        print("❌ 邮箱密码不能为空")
        return
    
    try:
        print(f"📧 连接IMAP邮箱: {test_email}")
        
        # 连接IMAP服务器
        mail = imaplib.IMAP4_SSL(IMAP_CONFIG['host'], IMAP_CONFIG['port'])
        
        # 登录
        mail.login(test_email, test_password)
        print("✅ IMAP邮箱登录成功")
        
        # 选择收件箱
        mail.select('inbox')
        
        # 获取邮箱统计信息
        status, messages = mail.search(None, 'ALL')
        if status == 'OK':
            email_count = len(messages[0].split()) if messages[0] else 0
            print(f"📊 收件箱邮件数量: {email_count}")
        
        # 搜索最近的邮件
        print(f"\n🔍 搜索最近的邮件...")
        
        search_criteria = [
            '(FROM "<EMAIL>")',
            '(FROM "<EMAIL>")',
            '(FROM "<EMAIL>")',
            '(SUBJECT "verification")',
            '(SUBJECT "verify")',
            '(SUBJECT "code")',
            'ALL'  # 如果没有Twitter邮件，显示所有邮件
        ]
        
        found_emails = False
        
        for i, criteria in enumerate(search_criteria, 1):
            try:
                print(f"\n🔍 搜索条件 {i}: {criteria}")
                status, messages = mail.search(None, criteria)
                
                if status == 'OK' and messages[0]:
                    email_ids = messages[0].split()
                    print(f"✅ 找到 {len(email_ids)} 封邮件")
                    
                    if email_ids:
                        found_emails = True
                        # 获取最新的3封邮件
                        latest_emails = email_ids[-3:] if len(email_ids) >= 3 else email_ids
                        
                        for j, email_id in enumerate(latest_emails, 1):
                            print(f"\n--- 邮件 {j} (ID: {email_id.decode()}) ---")
                            
                            try:
                                # 获取邮件内容
                                status, msg_data = mail.fetch(email_id, '(RFC822)')
                                
                                if status == 'OK':
                                    # 解析邮件
                                    raw_email = msg_data[0][1]
                                    print(f"📧 原始邮件数据类型: {type(raw_email)}")
                                    print(f"📧 原始邮件数据长度: {len(raw_email)} 字节")
                                    
                                    if isinstance(raw_email, bytes):
                                        email_message = email.parser.BytesParser().parsebytes(raw_email)
                                    else:
                                        email_message = email.parser.Parser().parsestr(str(raw_email))
                                    
                                    print("✅ 邮件解析成功")
                                    
                                    # 获取邮件主题
                                    subject_header = email_message.get("Subject", "")
                                    if subject_header:
                                        try:
                                            subject_parts = decode_header(subject_header)
                                            subject = ""
                                            for part, encoding in subject_parts:
                                                if isinstance(part, bytes):
                                                    if encoding:
                                                        subject += part.decode(encoding)
                                                    else:
                                                        subject += part.decode('utf-8', errors='ignore')
                                                else:
                                                    subject += str(part)
                                        except Exception as e:
                                            subject = str(subject_header)
                                            print(f"⚠️ 解析邮件主题失败: {str(e)}")
                                    else:
                                        subject = "无主题"
                                    
                                    print(f"📧 邮件主题: {subject}")
                                    
                                    # 获取发件人
                                    sender = email_message.get("From", "未知发件人")
                                    print(f"📧 发件人: {sender}")
                                    
                                    # 获取邮件内容
                                    email_body = ""
                                    email_html = ""
                                    
                                    if email_message.is_multipart():
                                        print("📧 多部分邮件，正在解析各部分...")
                                        for k, part in enumerate(email_message.walk()):
                                            content_type = part.get_content_type()
                                            print(f"📧 部分 {k}: {content_type}")
                                            
                                            if content_type == "text/plain":
                                                try:
                                                    payload = part.get_payload(decode=True)
                                                    if payload:
                                                        if isinstance(payload, bytes):
                                                            email_body = payload.decode('utf-8', errors='ignore')
                                                        else:
                                                            email_body = str(payload)
                                                        print(f"📧 找到纯文本内容，长度: {len(email_body)}")
                                                except Exception as e:
                                                    print(f"⚠️ 解析纯文本部分失败: {str(e)}")
                                            
                                            elif content_type == "text/html":
                                                try:
                                                    payload = part.get_payload(decode=True)
                                                    if payload:
                                                        if isinstance(payload, bytes):
                                                            email_html = payload.decode('utf-8', errors='ignore')
                                                        else:
                                                            email_html = str(payload)
                                                        print(f"📧 找到HTML内容，长度: {len(email_html)}")
                                                except Exception as e:
                                                    print(f"⚠️ 解析HTML部分失败: {str(e)}")
                                    else:
                                        print("📧 单部分邮件，直接解析...")
                                        try:
                                            payload = email_message.get_payload(decode=True)
                                            if payload:
                                                if isinstance(payload, bytes):
                                                    email_body = payload.decode('utf-8', errors='ignore')
                                                else:
                                                    email_body = str(payload)
                                                print(f"📧 邮件内容长度: {len(email_body)}")
                                        except Exception as e:
                                            print(f"⚠️ 解析邮件内容失败: {str(e)}")
                                            # 尝试直接获取payload
                                            email_body = str(email_message.get_payload())
                                    
                                    # 打印邮件内容
                                    if email_body:
                                        print(f"\n📧 邮件纯文本内容（前500字符）:")
                                        print("=" * 50)
                                        print(email_body[:500])
                                        print("=" * 50)
                                        
                                        # 提取验证码
                                        verification_code = extract_verification_code_from_text(email_body)
                                        if verification_code:
                                            print(f"✅ 从纯文本中找到验证码: {verification_code}")
                                        else:
                                            print("❌ 纯文本中未找到验证码")
                                    
                                    if email_html:
                                        print(f"\n📧 邮件HTML内容（前500字符）:")
                                        print("=" * 50)
                                        print(email_html[:500])
                                        print("=" * 50)
                                        
                                        # 提取验证码
                                        verification_code = extract_verification_code_from_text(email_html)
                                        if verification_code:
                                            print(f"✅ 从HTML中找到验证码: {verification_code}")
                                        else:
                                            print("❌ HTML中未找到验证码")
                                
                            except Exception as e:
                                print(f"❌ 处理邮件 {j} 时出错: {str(e)}")
                                import traceback
                                print(f"🔍 详细错误信息: {traceback.format_exc()}")
                        
                        # 如果找到了邮件，就不继续搜索了
                        if criteria != 'ALL':
                            break
                else:
                    print(f"❌ 搜索条件 {i} 无结果")
                    
            except Exception as e:
                print(f"❌ 搜索条件 {i} 执行失败: {str(e)}")
                continue
        
        if not found_emails:
            print("❌ 未找到任何邮件")
        
        # 关闭连接
        mail.close()
        mail.logout()
        print("\n✅ IMAP连接测试完成")
        
    except Exception as e:
        print(f"❌ IMAP连接失败: {str(e)}")
        import traceback
        print(f"🔍 详细错误信息: {traceback.format_exc()}")


def extract_verification_code_from_text(text):
    """从邮件文本中提取验证码"""
    try:
        print(f"🔍 开始从文本中提取验证码...")
        print(f"📝 文本长度: {len(text)} 字符")
        
        # 常见的验证码模式（按优先级排序）
        patterns = [
            # Twitter特定模式
            r'twitter.*?verification.*?code[:\s]*([0-9]{4,8})',
            r'twitter.*?code[:\s]*([0-9]{4,8})',
            r'x\.com.*?code[:\s]*([0-9]{4,8})',
            
            # 通用验证码模式
            r'verification\s*code[:\s]*([0-9]{4,8})',
            r'verify.*?code[:\s]*([0-9]{4,8})',
            r'confirmation\s*code[:\s]*([0-9]{4,8})',
            r'security\s*code[:\s]*([0-9]{4,8})',
            r'access\s*code[:\s]*([0-9]{4,8})',
            
            # 简单模式
            r'code[:\s]*([0-9]{4,8})',
            r'verify[:\s]*([0-9]{4,8})',
            r'confirm[:\s]*([0-9]{4,8})',
            
            # HTML模式
            r'<.*?>([0-9]{6})<.*?>',
            r'<strong>([0-9]{4,8})</strong>',
            r'<b>([0-9]{4,8})</b>',
            
            # 独立数字模式（最后尝试）
            r'\b([0-9]{6})\b',  # 6位数字
            r'\b([0-9]{5})\b',  # 5位数字
            r'\b([0-9]{4})\b',  # 4位数字
            r'\b([0-9]{8})\b',  # 8位数字
        ]
        
        for i, pattern in enumerate(patterns, 1):
            print(f"🔍 尝试模式 {i}: {pattern}")
            try:
                matches = re.findall(pattern, text, re.IGNORECASE | re.DOTALL)
                if matches:
                    print(f"✅ 模式 {i} 找到匹配: {matches}")
                    
                    for match in matches:
                        code = match.strip()
                        # 验证码通常是4-8位数字
                        if code.isdigit() and 4 <= len(code) <= 8:
                            print(f"✅ 验证码验证通过: {code}")
                            return code
                        else:
                            print(f"⚠️ 验证码格式不符合要求: {code} (长度: {len(code)})")
                else:
                    print(f"❌ 模式 {i} 无匹配")
            except Exception as e:
                print(f"❌ 模式 {i} 执行失败: {str(e)}")
                continue
        
        # 如果所有模式都失败，尝试查找所有数字并分析
        print(f"🔍 尝试查找所有数字...")
        all_numbers = re.findall(r'\b\d+\b', text)
        if all_numbers:
            print(f"📊 找到的所有数字: {all_numbers}")
            
            # 过滤出可能的验证码
            potential_codes = [num for num in all_numbers if 4 <= len(num) <= 8]
            if potential_codes:
                print(f"🎯 可能的验证码: {potential_codes}")
                return potential_codes[0]  # 返回第一个可能的验证码
        
        print(f"❌ 未找到验证码")
        return None
        
    except Exception as e:
        print(f"❌ 提取验证码时出错: {str(e)}")
        import traceback
        print(f"🔍 详细错误信息: {traceback.format_exc()}")
        return None


if __name__ == '__main__':
    print("🚀 开始测试IMAP和验证码提取功能")
    print("=" * 60)
    
    try:
        test_imap_connection()
        
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {str(e)}")
