import json
import os
import sys
import time
import shutil
import re

import httpx
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common import NoSuchElementException, TimeoutException, ElementClickInterceptedException

from ixbrowser_local_api import IXBrowserClient

# 测试单个账户的 Zealy.io 钱包授权登录

def get_profile_list(name=None, timeout=20.0):
    """获取配置文件列表，包含备注信息"""
    client = None
    try:
        client = httpx.Client(timeout=timeout)
        url = "http://localhost:53200/api/v2/profile-list"
        params = {"page": 1, "limit": 100}

        if name:
            params["name"] = name

        response = client.post(url, json=params)
        data = response.json()

        if response.status_code == 200 and data.get("error", {}).get("code") == 0:
            profiles = data.get("data", {}).get("data", [])
            return profiles, None, None

        error_code = data.get("error", {}).get("code")
        error_message = data.get("error", {}).get("message")
        return None, error_code, error_message

    except Exception as e:
        return None, None, str(e)
    finally:
        if client:
            client.close()


def list_available_profiles():
    """列出所有可用的浏览器配置文件"""
    try:
        print("📋 获取可用的浏览器配置文件...")
        
        profiles, error_code, error_message = get_profile_list()
        
        if profiles is None:
            print(f"❌ 获取配置文件列表失败: {error_message}")
            return []
            
        print(f"✅ 找到 {len(profiles)} 个配置文件:")
        print("-" * 80)
        
        for i, profile in enumerate(profiles, 1):
            profile_id = profile.get('id')
            profile_name = profile.get('name', '未命名')
            note = profile.get('note', '无备注')
            
            print(f"{i:2d}. ID: {profile_id}")
            print(f"    名称: {profile_name}")
            print(f"    备注: {note[:100]}{'...' if len(note) > 100 else ''}")
            print("-" * 80)
        
        return profiles
        
    except Exception as e:
        print(f"❌ 列出配置文件时出错: {str(e)}")
        return []


def open_browser_by_profile_id(profile_id):
    """通过配置文件ID打开浏览器"""
    try:
        print(f"🌐 正在打开浏览器配置文件: {profile_id}")
        
        client = IXBrowserClient()
        response = client.open_browser(profile_id)
        
        if response.get("code") == 0:
            data = response.get("data", {})
            driver_path = data.get("driver_path")
            debugger_address = data.get("debugger_address")
            
            print(f"✅ 浏览器启动成功")
            print(f"📍 Driver路径: {driver_path}")
            print(f"🔗 调试地址: {debugger_address}")
            
            # 连接到浏览器
            options = Options()
            options.add_experimental_option("debuggerAddress", debugger_address)
            driver = webdriver.Chrome(executable_path=driver_path, options=options)
            
            return driver
        else:
            error_message = response.get("message", "未知错误")
            print(f"❌ 浏览器启动失败: {error_message}")
            return None
            
    except Exception as e:
        print(f"❌ 打开浏览器时出错: {str(e)}")
        return None


def close_browser_by_profile_id(profile_id):
    """通过配置文件ID关闭浏览器"""
    try:
        print(f"🔒 正在关闭浏览器配置文件: {profile_id}")
        
        client = IXBrowserClient()
        response = client.close_browser(profile_id)
        
        if response.get("code") == 0:
            print(f"✅ 浏览器关闭成功")
            return True
        else:
            error_message = response.get("message", "未知错误")
            print(f"❌ 浏览器关闭失败: {error_message}")
            return False
            
    except Exception as e:
        print(f"❌ 关闭浏览器时出错: {str(e)}")
        return False


def navigate_to_zealy(driver):
    """导航到 Zealy.io 网站"""
    try:
        print("🌐 正在访问 Zealy.io...")
        driver.get("https://zealy.io/cw/coingarage/questboard/")
        
        # 等待页面加载
        time.sleep(8)
        
        print(f"📍 当前URL: {driver.current_url}")
        print(f"📄 页面标题: {driver.title}")
        
        # 检查页面是否正确加载
        if "zealy" in driver.current_url.lower():
            print("✅ 成功访问 Zealy.io")
            return True
        else:
            print(f"❌ 页面加载异常，当前URL: {driver.current_url}")
            return False
            
    except Exception as e:
        print(f"❌ 访问 Zealy.io 时出错: {str(e)}")
        return False


def find_and_analyze_login_elements(driver):
    """查找并分析页面上的登录相关元素"""
    try:
        print("🔍 分析页面上的登录相关元素...")
        
        # 查找可能的登录按钮
        login_keywords = ['login', 'sign in', 'connect', 'join', '登录', '连接']
        
        found_elements = []
        
        for keyword in login_keywords:
            # 查找包含关键词的按钮
            buttons = driver.find_elements(By.XPATH, f"//button[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), '{keyword}')]")
            for btn in buttons:
                if btn.is_displayed():
                    found_elements.append({
                        'type': 'button',
                        'text': btn.text,
                        'tag': btn.tag_name,
                        'class': btn.get_attribute('class'),
                        'element': btn
                    })
            
            # 查找包含关键词的链接
            links = driver.find_elements(By.XPATH, f"//a[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), '{keyword}')]")
            for link in links:
                if link.is_displayed():
                    found_elements.append({
                        'type': 'link',
                        'text': link.text,
                        'tag': link.tag_name,
                        'class': link.get_attribute('class'),
                        'href': link.get_attribute('href'),
                        'element': link
                    })
            
            # 查找包含关键词的div
            divs = driver.find_elements(By.XPATH, f"//div[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), '{keyword}')]")
            for div in divs:
                if div.is_displayed() and div.text.strip():
                    found_elements.append({
                        'type': 'div',
                        'text': div.text,
                        'tag': div.tag_name,
                        'class': div.get_attribute('class'),
                        'element': div
                    })
        
        if found_elements:
            print(f"✅ 找到 {len(found_elements)} 个可能的登录元素:")
            for i, elem in enumerate(found_elements, 1):
                print(f"{i:2d}. {elem['type'].upper()}: '{elem['text'][:50]}{'...' if len(elem['text']) > 50 else ''}'")
                print(f"    Class: {elem.get('class', 'N/A')}")
                if 'href' in elem:
                    print(f"    Href: {elem['href']}")
                print("-" * 60)
        else:
            print("❌ 未找到明显的登录元素")
        
        return found_elements
        
    except Exception as e:
        print(f"❌ 分析登录元素时出错: {str(e)}")
        return []


def try_click_login_element(driver, element_info):
    """尝试点击登录元素"""
    try:
        element = element_info['element']
        element_text = element_info['text']
        
        print(f"🔍 尝试点击: {element_text}")
        
        # 滚动到元素位置
        driver.execute_script("arguments[0].scrollIntoView(true);", element)
        time.sleep(1)
        
        # 尝试点击
        element.click()
        print(f"✅ 成功点击: {element_text}")
        time.sleep(5)
        
        return True
        
    except Exception as e:
        print(f"❌ 点击失败: {str(e)}")
        return False


def find_wallet_options(driver):
    """查找钱包连接选项"""
    try:
        print("🔍 查找钱包连接选项...")
        
        # 等待页面加载
        time.sleep(3)
        
        wallet_keywords = ['okx', 'metamask', 'wallet', 'connect wallet', '钱包']
        found_wallets = []
        
        for keyword in wallet_keywords:
            # 查找包含关键词的元素
            elements = driver.find_elements(By.XPATH, f"//*[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), '{keyword}')]")
            for elem in elements:
                if elem.is_displayed() and elem.text.strip():
                    found_wallets.append({
                        'text': elem.text,
                        'tag': elem.tag_name,
                        'class': elem.get_attribute('class'),
                        'element': elem
                    })
            
            # 查找包含关键词的图片
            images = driver.find_elements(By.XPATH, f"//img[contains(translate(@alt, 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), '{keyword}') or contains(translate(@src, 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), '{keyword}')]")
            for img in images:
                if img.is_displayed():
                    parent = img.find_element(By.XPATH, "..")
                    found_wallets.append({
                        'text': f"Image: {img.get_attribute('alt') or img.get_attribute('src')}",
                        'tag': parent.tag_name,
                        'class': parent.get_attribute('class'),
                        'element': parent
                    })
        
        if found_wallets:
            print(f"✅ 找到 {len(found_wallets)} 个钱包选项:")
            for i, wallet in enumerate(found_wallets, 1):
                print(f"{i:2d}. {wallet['tag'].upper()}: '{wallet['text'][:50]}{'...' if len(wallet['text']) > 50 else ''}'")
                print(f"    Class: {wallet.get('class', 'N/A')}")
                print("-" * 60)
        else:
            print("❌ 未找到钱包连接选项")
        
        return found_wallets
        
    except Exception as e:
        print(f"❌ 查找钱包选项时出错: {str(e)}")
        return []


def check_window_handles(driver):
    """检查当前窗口句柄"""
    try:
        print("🔍 检查当前窗口状态...")
        
        all_windows = driver.window_handles
        current_window = driver.current_window_handle
        
        print(f"📋 总窗口数: {len(all_windows)}")
        print(f"📋 当前窗口: {current_window}")
        
        for i, window_handle in enumerate(all_windows, 1):
            try:
                driver.switch_to.window(window_handle)
                title = driver.title
                url = driver.current_url
                print(f"{i}. 窗口 {window_handle[:8]}...")
                print(f"   标题: {title}")
                print(f"   URL: {url}")
                print("-" * 60)
            except Exception as e:
                print(f"   ❌ 无法访问窗口: {str(e)}")
        
        # 切换回原窗口
        driver.switch_to.window(current_window)
        
    except Exception as e:
        print(f"❌ 检查窗口时出错: {str(e)}")


def interactive_test():
    """交互式测试"""
    try:
        print("🚀 Zealy.io 登录测试脚本")
        print("=" * 60)
        
        # 1. 列出可用的配置文件
        profiles = list_available_profiles()
        if not profiles:
            print("❌ 没有可用的配置文件")
            return
        
        # 2. 选择配置文件
        while True:
            try:
                choice = input(f"\n请选择配置文件 (1-{len(profiles)}) 或输入 'q' 退出: ").strip()
                if choice.lower() == 'q':
                    return
                
                choice_num = int(choice)
                if 1 <= choice_num <= len(profiles):
                    selected_profile = profiles[choice_num - 1]
                    profile_id = selected_profile['id']
                    profile_name = selected_profile['name']
                    break
                else:
                    print(f"❌ 请输入 1-{len(profiles)} 之间的数字")
            except ValueError:
                print("❌ 请输入有效的数字")
        
        print(f"\n✅ 选择了配置文件: {profile_name} (ID: {profile_id})")
        
        # 3. 打开浏览器
        driver = open_browser_by_profile_id(profile_id)
        if not driver:
            print("❌ 浏览器打开失败")
            return
        
        try:
            # 4. 访问 Zealy.io
            if not navigate_to_zealy(driver):
                print("❌ 访问 Zealy.io 失败")
                return
            
            # 5. 分析页面元素
            login_elements = find_and_analyze_login_elements(driver)
            
            if login_elements:
                # 6. 选择登录元素
                while True:
                    try:
                        choice = input(f"\n请选择要点击的登录元素 (1-{len(login_elements)}) 或输入 's' 跳过: ").strip()
                        if choice.lower() == 's':
                            break
                        
                        choice_num = int(choice)
                        if 1 <= choice_num <= len(login_elements):
                            selected_element = login_elements[choice_num - 1]
                            
                            # 尝试点击
                            if try_click_login_element(driver, selected_element):
                                # 7. 查找钱包选项
                                wallet_options = find_wallet_options(driver)
                                
                                if wallet_options:
                                    # 选择钱包选项
                                    while True:
                                        try:
                                            wallet_choice = input(f"\n请选择钱包选项 (1-{len(wallet_options)}) 或输入 's' 跳过: ").strip()
                                            if wallet_choice.lower() == 's':
                                                break
                                            
                                            wallet_num = int(wallet_choice)
                                            if 1 <= wallet_num <= len(wallet_options):
                                                selected_wallet = wallet_options[wallet_num - 1]
                                                
                                                # 点击钱包选项
                                                try:
                                                    wallet_element = selected_wallet['element']
                                                    driver.execute_script("arguments[0].scrollIntoView(true);", wallet_element)
                                                    time.sleep(1)
                                                    wallet_element.click()
                                                    print(f"✅ 成功点击钱包选项: {selected_wallet['text']}")
                                                    time.sleep(5)
                                                    
                                                    # 检查窗口变化
                                                    check_window_handles(driver)
                                                    
                                                except Exception as e:
                                                    print(f"❌ 点击钱包选项失败: {str(e)}")
                                                
                                                break
                                            else:
                                                print(f"❌ 请输入 1-{len(wallet_options)} 之间的数字")
                                        except ValueError:
                                            print("❌ 请输入有效的数字")
                            break
                        else:
                            print(f"❌ 请输入 1-{len(login_elements)} 之间的数字")
                    except ValueError:
                        print("❌ 请输入有效的数字")
            
            # 8. 等待用户操作
            input("\n按回车键继续...")
            
        finally:
            # 清理资源
            try:
                driver.quit()
                print("🔒 浏览器已关闭")
            except:
                pass
            
            try:
                close_browser_by_profile_id(profile_id)
            except:
                pass
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {str(e)}")
        import traceback
        print(f"🔍 详细错误信息: {traceback.format_exc()}")


if __name__ == "__main__":
    interactive_test()
