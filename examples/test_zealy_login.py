import json
import os
import sys
import time
import shutil
import re

import httpx
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common import NoSuchElementException, TimeoutException, ElementClickInterceptedException

from ixbrowser_local_api import IXBrowserClient

# 测试单个账户的 Zealy.io 钱包授权登录

def get_profile_list(name=None, timeout=20.0):
    """获取配置文件列表，包含备注信息"""
    client = None
    try:
        client = httpx.Client(timeout=timeout)
        url = "http://localhost:53200/api/v2/profile-list"
        params = {"page": 1, "limit": 100}

        if name:
            params["name"] = name

        response = client.post(url, json=params)
        data = response.json()

        if response.status_code == 200 and data.get("error", {}).get("code") == 0:
            profiles = data.get("data", {}).get("data", [])
            return profiles, None, None

        error_code = data.get("error", {}).get("code")
        error_message = data.get("error", {}).get("message")
        return None, error_code, error_message

    except Exception as e:
        return None, None, str(e)
    finally:
        if client:
            client.close()


def list_available_profiles():
    """列出所有可用的浏览器配置文件"""
    try:
        print("📋 获取可用的浏览器配置文件...")
        
        profiles, error_code, error_message = get_profile_list()
        
        if profiles is None:
            print(f"❌ 获取配置文件列表失败: {error_message}")
            return []
            
        print(f"✅ 找到 {len(profiles)} 个配置文件:")
        print("-" * 80)
        
        for i, profile in enumerate(profiles, 1):
            # 尝试不同的ID字段名
            profile_id = profile.get('id') or profile.get('profile_id') or profile.get('profileId')
            profile_name = profile.get('name', '未命名')
            note = profile.get('note', '无备注')

            print(f"{i:2d}. ID: {profile_id}")
            print(f"    名称: {profile_name}")
            print(f"    备注: {note[:100]}{'...' if len(note) > 100 else ''}")

            # 调试：显示完整的profile数据结构
            if i == 1:  # 只显示第一个profile的完整结构
                print(f"    调试信息 - Profile字段: {list(profile.keys())}")

            print("-" * 80)
        
        return profiles
        
    except Exception as e:
        print(f"❌ 列出配置文件时出错: {str(e)}")
        return []


def open_browser_by_profile_id(profile_id):
    """通过配置文件ID打开浏览器"""
    try:
        print(f"🌐 正在打开浏览器配置文件: {profile_id}")

        client = IXBrowserClient()

        # 1. 打开浏览器
        try:
            open_result = client.open_profile(profile_id, load_profile_info_page=False)

            if not open_result:
                print(f"❌ 打开浏览器失败: {client.code} - {client.message}")
                return None

            print(f"✅ 浏览器打开成功")
            print(f"🔍 打开结果: {open_result}")

        except Exception as e:
            print(f"❌ 打开浏览器时出错: {str(e)}")
            return None

        # 2. 获取调试地址并连接Selenium
        debug_address = open_result.get('debugging_address', '')
        if debug_address.startswith('http://'):
            debug_address = debug_address[7:]
        elif debug_address.startswith('https://'):
            debug_address = debug_address[8:]

        print(f"🔗 调试地址: {debug_address}")

        chrome_options = Options()
        chrome_options.debugger_address = debug_address

        driver = None
        try:
            # 初始化WebDriver
            webdriver_path = open_result.get('webdriver', '')
            print(f"📍 WebDriver路径: {webdriver_path}")

            if webdriver_path:
                service = webdriver.chrome.service.Service(executable_path=webdriver_path)
                driver = webdriver.Chrome(service=service, options=chrome_options)
            else:
                driver = webdriver.Chrome(options=chrome_options)

            print("✅ WebDriver连接成功")
            return driver

        except Exception as e:
            print(f"❌ WebDriver连接失败: {str(e)}")
            import traceback
            print(f"🔍 详细错误信息: {traceback.format_exc()}")
            return None

    except Exception as e:
        print(f"❌ 打开浏览器时出错: {str(e)}")
        import traceback
        print(f"🔍 详细错误信息: {traceback.format_exc()}")
        return None


def close_browser_by_profile_id(profile_id):
    """通过配置文件ID关闭浏览器"""
    try:
        print(f"🔒 正在关闭浏览器配置文件: {profile_id}")

        client = IXBrowserClient()
        close_result = client.close_profile(profile_id)

        if close_result:
            print("✅ 浏览器已关闭")
            return True
        else:
            print("⚠️ 浏览器关闭可能失败")
            return False

    except Exception as e:
        print(f"❌ 关闭浏览器时出错: {str(e)}")
        return False


def check_zealy_login_status(driver):
    """检查 Zealy.io 登录状态"""
    try:
        print("🔍 检查 Zealy.io 登录状态...")

        # 等待页面加载
        time.sleep(3)

        # 处理可能的弹窗
        handle_popups(driver)

        # 检查登录状态的指标元素
        login_indicators = [
            # 用户头像或用户菜单
            "//img[contains(@class, 'avatar')]",
            "//div[contains(@class, 'avatar')]",
            "//button[contains(@class, 'avatar')]",
            # 用户名或用户信息
            "//div[contains(@class, 'user')]",
            "//span[contains(@class, 'username')]",
            "//div[contains(@class, 'profile')]",
            # 登录后的导航元素
            "//nav[contains(@class, 'user')]",
            "//div[contains(@class, 'user-menu')]",
            "//button[contains(@class, 'user-menu')]",
            # 钱包连接状态
            "//div[contains(@class, 'wallet-connected')]",
            "//button[contains(@class, 'wallet-connected')]",
            "//span[contains(text(), 'Connected')]",
            "//span[contains(text(), 'connected')]",
            # 断开连接按钮（说明已连接）
            "//button[contains(text(), 'Disconnect')]",
            "//button[contains(text(), 'disconnect')]",
            "//a[contains(text(), 'Disconnect')]",
            # 用户相关的下拉菜单
            "//div[@role='menu']",
            "//ul[contains(@class, 'dropdown')]",
            # 个人资料链接
            "//a[contains(@href, 'profile')]",
            "//a[contains(@href, 'account')]",
            "//a[contains(@href, 'settings')]"
        ]

        print("🔍 检查登录指标元素...")
        for indicator in login_indicators:
            try:
                elements = driver.find_elements(By.XPATH, indicator)
                for element in elements:
                    if element.is_displayed():
                        element_text = element.text.strip() if hasattr(element, 'text') else ''
                        element_class = element.get_attribute('class') or ''
                        print(f"✅ 找到登录指标: {indicator}")
                        print(f"   元素文本: '{element_text}'")
                        print(f"   元素类名: '{element_class}'")
                        print("✅ 检测到已登录状态")
                        return True
            except Exception as e:
                continue

        # 检查页面URL是否包含登录后的特征
        current_url = driver.current_url
        if any(keyword in current_url.lower() for keyword in ['dashboard', 'profile', 'account', 'user']):
            print(f"✅ URL显示已登录状态: {current_url}")
            return True

        # 检查页面标题是否包含登录后的特征
        page_title = driver.title.lower()
        if any(keyword in page_title for keyword in ['dashboard', 'profile', 'account']):
            print(f"✅ 页面标题显示已登录状态: {driver.title}")
            return True

        print("❌ 未检测到登录状态")
        return False

    except Exception as e:
        print(f"❌ 检查登录状态时出错: {str(e)}")
        return False


def check_login_status_via_login_page(driver):
    """通过访问登录页面检测登录状态"""
    try:
        print("🔍 通过登录页面检测登录状态...")
        driver.get("https://zealy.io/login")

        # 等待页面加载
        time.sleep(5)

        print(f"📍 当前URL: {driver.current_url}")
        print(f"📄 页面标题: {driver.title}")

        # 处理可能的弹窗
        handle_popups(driver)

        # 如果已经登录，通常会被重定向到其他页面（如dashboard）
        current_url = driver.current_url.lower()

        # 检查是否被重定向到非登录页面
        if "/login" not in current_url:
            print(f"✅ 检测到已登录状态 - 被重定向到: {driver.current_url}")
            return True

        # 检查页面上是否有登录表单元素
        login_form_indicators = [
            "//input[@type='email']",
            "//input[@type='password']",
            "//button[contains(text(), 'Sign in')]",
            "//button[contains(text(), 'Login')]",
            "//button[contains(text(), 'Connect')]",
            "//div[contains(@class, 'login')]",
            "//form[contains(@class, 'login')]"
        ]

        has_login_form = False
        for indicator in login_form_indicators:
            try:
                elements = driver.find_elements(By.XPATH, indicator)
                if elements and any(elem.is_displayed() for elem in elements):
                    print(f"🔍 找到登录表单元素: {indicator}")
                    has_login_form = True
                    break
            except:
                continue

        if has_login_form:
            print("❌ 检测到登录表单，账户未登录")
            return False
        else:
            print("✅ 未找到登录表单，可能已登录")
            return True

    except Exception as e:
        print(f"❌ 检测登录状态时出错: {str(e)}")
        return False


def navigate_to_zealy(driver):
    """导航到 Zealy.io 网站"""
    try:
        print("🌐 正在检测 Zealy.io 登录状态...")

        # 首先检查登录状态
        if check_login_status_via_login_page(driver):
            print("✅ 检测到已登录状态，跳过登录流程")
            # 导航到目标页面
            print("🌐 导航到目标页面...")
            driver.get("https://zealy.io/cw/coingarage/questboard/")
            time.sleep(5)
            return "already_logged_in"

        print("❌ 检测到未登录状态，需要进行登录")
        print("🌐 正在访问 Zealy.io 主页...")
        driver.get("https://zealy.io/cw/coingarage/questboard/")

        # 等待页面加载
        time.sleep(8)

        print(f"📍 当前URL: {driver.current_url}")
        print(f"📄 页面标题: {driver.title}")

        # 处理可能的弹窗
        handle_popups(driver)

        # 检查页面是否正确加载
        if "zealy" in driver.current_url.lower():
            print("✅ 成功访问 Zealy.io，需要进行登录")
            return True
        else:
            print(f"❌ 页面加载异常，当前URL: {driver.current_url}")
            return False

    except Exception as e:
        print(f"❌ 访问 Zealy.io 时出错: {str(e)}")
        return False


def handle_popups(driver):
    """处理页面弹窗"""
    try:
        print("🔍 检查并处理页面弹窗...")

        # 等待弹窗出现
        time.sleep(3)

        # 常见的弹窗关闭按钮选择器
        close_selectors = [
            "//button[contains(@class, 'close')]",
            "//button[contains(@aria-label, 'close')]",
            "//button[contains(@aria-label, 'Close')]",
            "//button[contains(text(), '×')]",
            "//button[contains(text(), 'Close')]",
            "//button[contains(text(), 'close')]",
            "//div[contains(@class, 'close')]",
            "//span[contains(@class, 'close')]",
            "//i[contains(@class, 'close')]",
            "//button[contains(@class, 'modal-close')]",
            "//button[contains(@class, 'popup-close')]",
            "//div[contains(@class, 'modal-close')]",
            "//div[contains(@class, 'popup-close')]",
            "//button[@type='button' and contains(@class, 'btn-close')]",
            "//button[contains(@class, 'dismiss')]",
            "//button[contains(@data-dismiss, 'modal')]",
            "//div[contains(@role, 'dialog')]//button",
            "//div[contains(@class, 'modal')]//button[contains(@class, 'close')]",
            "//div[contains(@class, 'popup')]//button[contains(@class, 'close')]"
        ]

        popup_closed = False
        for selector in close_selectors:
            try:
                elements = driver.find_elements(By.XPATH, selector)
                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        print(f"🔍 找到弹窗关闭按钮: {selector}")
                        element.click()
                        print("✅ 成功关闭弹窗")
                        time.sleep(2)
                        popup_closed = True
                        break
                if popup_closed:
                    break
            except Exception as e:
                continue

        if not popup_closed:
            print("ℹ️ 未发现需要关闭的弹窗")

        # 尝试按ESC键关闭弹窗
        try:
            from selenium.webdriver.common.keys import Keys
            driver.find_element(By.TAG_NAME, 'body').send_keys(Keys.ESCAPE)
            print("🔍 尝试按ESC键关闭弹窗")
            time.sleep(2)
        except:
            pass

    except Exception as e:
        print(f"⚠️ 处理弹窗时出错: {str(e)}")


def analyze_page_structure(driver):
    """分析页面结构"""
    try:
        print("🔍 分析页面结构...")

        # 获取页面源码的前1000个字符
        page_source = driver.page_source[:1000]
        print(f"📄 页面源码片段: {page_source}")

        # 查找所有按钮
        buttons = driver.find_elements(By.TAG_NAME, "button")
        print(f"🔘 找到 {len(buttons)} 个按钮:")
        for i, btn in enumerate(buttons[:10]):  # 只显示前10个
            try:
                if btn.is_displayed():
                    text = btn.text.strip()
                    classes = btn.get_attribute('class')
                    print(f"  {i+1}. 按钮文本: '{text}' | 类名: {classes}")
            except:
                continue

        # 查找所有链接
        links = driver.find_elements(By.TAG_NAME, "a")
        print(f"🔗 找到 {len(links)} 个链接:")
        for i, link in enumerate(links[:10]):  # 只显示前10个
            try:
                if link.is_displayed():
                    text = link.text.strip()
                    href = link.get_attribute('href')
                    print(f"  {i+1}. 链接文本: '{text}' | 链接: {href}")
            except:
                continue

        # 查找所有div
        divs = driver.find_elements(By.TAG_NAME, "div")
        clickable_divs = []
        for div in divs[:20]:  # 只检查前20个div
            try:
                if div.is_displayed() and div.text.strip():
                    onclick = div.get_attribute('onclick')
                    cursor = driver.execute_script("return window.getComputedStyle(arguments[0]).cursor;", div)
                    if onclick or cursor == 'pointer':
                        clickable_divs.append(div)
            except:
                continue

        print(f"👆 找到 {len(clickable_divs)} 个可点击的div:")
        for i, div in enumerate(clickable_divs[:5]):  # 只显示前5个
            try:
                text = div.text.strip()[:50]
                classes = div.get_attribute('class')
                print(f"  {i+1}. Div文本: '{text}' | 类名: {classes}")
            except:
                continue

    except Exception as e:
        print(f"❌ 分析页面结构时出错: {str(e)}")


def find_and_analyze_login_elements(driver):
    """查找并分析页面上的登录相关元素"""
    try:
        print("🔍 分析页面上的登录相关元素...")

        # 先处理弹窗
        handle_popups(driver)

        # 分析页面结构
        analyze_page_structure(driver)

        # 查找可能的登录按钮
        login_keywords = ['login', 'sign in', 'connect', 'join', '登录', '连接', 'wallet', 'connect wallet']

        found_elements = []

        for keyword in login_keywords:
            # 查找包含关键词的按钮
            buttons = driver.find_elements(By.XPATH, f"//button[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), '{keyword}')]")
            for btn in buttons:
                if btn.is_displayed():
                    found_elements.append({
                        'type': 'button',
                        'text': btn.text,
                        'tag': btn.tag_name,
                        'class': btn.get_attribute('class'),
                        'element': btn
                    })

            # 查找包含关键词的链接
            links = driver.find_elements(By.XPATH, f"//a[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), '{keyword}')]")
            for link in links:
                if link.is_displayed():
                    found_elements.append({
                        'type': 'link',
                        'text': link.text,
                        'tag': link.tag_name,
                        'class': link.get_attribute('class'),
                        'href': link.get_attribute('href'),
                        'element': link
                    })

            # 查找包含关键词的div
            divs = driver.find_elements(By.XPATH, f"//div[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), '{keyword}')]")
            for div in divs:
                if div.is_displayed() and div.text.strip():
                    found_elements.append({
                        'type': 'div',
                        'text': div.text,
                        'tag': div.tag_name,
                        'class': div.get_attribute('class'),
                        'element': div
                    })

        if found_elements:
            print(f"✅ 找到 {len(found_elements)} 个可能的登录元素:")
            for i, elem in enumerate(found_elements, 1):
                print(f"{i:2d}. {elem['type'].upper()}: '{elem['text'][:50]}{'...' if len(elem['text']) > 50 else ''}'")
                print(f"    Class: {elem.get('class', 'N/A')}")
                if 'href' in elem:
                    print(f"    Href: {elem['href']}")
                print("-" * 60)
        else:
            print("❌ 未找到明显的登录元素")

        return found_elements

    except Exception as e:
        print(f"❌ 分析登录元素时出错: {str(e)}")
        return []


def find_and_analyze_login_elements(driver):
    """查找并分析页面上的登录相关元素"""
    try:
        print("🔍 分析页面上的登录相关元素...")
        
        # 查找可能的登录按钮
        login_keywords = ['login', 'sign in', 'connect', 'join', '登录', '连接']
        
        found_elements = []
        
        for keyword in login_keywords:
            # 查找包含关键词的按钮
            buttons = driver.find_elements(By.XPATH, f"//button[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), '{keyword}')]")
            for btn in buttons:
                if btn.is_displayed():
                    found_elements.append({
                        'type': 'button',
                        'text': btn.text,
                        'tag': btn.tag_name,
                        'class': btn.get_attribute('class'),
                        'element': btn
                    })
            
            # 查找包含关键词的链接
            links = driver.find_elements(By.XPATH, f"//a[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), '{keyword}')]")
            for link in links:
                if link.is_displayed():
                    found_elements.append({
                        'type': 'link',
                        'text': link.text,
                        'tag': link.tag_name,
                        'class': link.get_attribute('class'),
                        'href': link.get_attribute('href'),
                        'element': link
                    })
            
            # 查找包含关键词的div
            divs = driver.find_elements(By.XPATH, f"//div[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), '{keyword}')]")
            for div in divs:
                if div.is_displayed() and div.text.strip():
                    found_elements.append({
                        'type': 'div',
                        'text': div.text,
                        'tag': div.tag_name,
                        'class': div.get_attribute('class'),
                        'element': div
                    })
        
        if found_elements:
            print(f"✅ 找到 {len(found_elements)} 个可能的登录元素:")
            for i, elem in enumerate(found_elements, 1):
                print(f"{i:2d}. {elem['type'].upper()}: '{elem['text'][:50]}{'...' if len(elem['text']) > 50 else ''}'")
                print(f"    Class: {elem.get('class', 'N/A')}")
                if 'href' in elem:
                    print(f"    Href: {elem['href']}")
                print("-" * 60)
        else:
            print("❌ 未找到明显的登录元素")
        
        return found_elements
        
    except Exception as e:
        print(f"❌ 分析登录元素时出错: {str(e)}")
        return []


def try_click_login_element(driver, element_info):
    """尝试点击登录元素"""
    try:
        element = element_info['element']
        element_text = element_info['text']
        
        print(f"🔍 尝试点击: {element_text}")
        
        # 滚动到元素位置
        driver.execute_script("arguments[0].scrollIntoView(true);", element)
        time.sleep(1)
        
        # 尝试点击
        element.click()
        print(f"✅ 成功点击: {element_text}")
        time.sleep(5)
        
        return True
        
    except Exception as e:
        print(f"❌ 点击失败: {str(e)}")
        return False


def find_wallet_options(driver):
    """查找钱包连接选项"""
    try:
        print("🔍 查找钱包连接选项...")
        
        # 等待页面加载
        time.sleep(3)
        
        wallet_keywords = ['okx', 'metamask', 'wallet', 'connect wallet', '钱包']
        found_wallets = []
        
        for keyword in wallet_keywords:
            # 查找包含关键词的元素
            elements = driver.find_elements(By.XPATH, f"//*[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), '{keyword}')]")
            for elem in elements:
                if elem.is_displayed() and elem.text.strip():
                    found_wallets.append({
                        'text': elem.text,
                        'tag': elem.tag_name,
                        'class': elem.get_attribute('class'),
                        'element': elem
                    })
            
            # 查找包含关键词的图片
            images = driver.find_elements(By.XPATH, f"//img[contains(translate(@alt, 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), '{keyword}') or contains(translate(@src, 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), '{keyword}')]")
            for img in images:
                if img.is_displayed():
                    parent = img.find_element(By.XPATH, "..")
                    found_wallets.append({
                        'text': f"Image: {img.get_attribute('alt') or img.get_attribute('src')}",
                        'tag': parent.tag_name,
                        'class': parent.get_attribute('class'),
                        'element': parent
                    })
        
        if found_wallets:
            print(f"✅ 找到 {len(found_wallets)} 个钱包选项:")
            for i, wallet in enumerate(found_wallets, 1):
                print(f"{i:2d}. {wallet['tag'].upper()}: '{wallet['text'][:50]}{'...' if len(wallet['text']) > 50 else ''}'")
                print(f"    Class: {wallet.get('class', 'N/A')}")
                print("-" * 60)
        else:
            print("❌ 未找到钱包连接选项")
        
        return found_wallets
        
    except Exception as e:
        print(f"❌ 查找钱包选项时出错: {str(e)}")
        return []


def check_window_handles(driver):
    """检查当前窗口句柄"""
    try:
        print("🔍 检查当前窗口状态...")
        
        all_windows = driver.window_handles
        current_window = driver.current_window_handle
        
        print(f"📋 总窗口数: {len(all_windows)}")
        print(f"📋 当前窗口: {current_window}")
        
        for i, window_handle in enumerate(all_windows, 1):
            try:
                driver.switch_to.window(window_handle)
                title = driver.title
                url = driver.current_url
                print(f"{i}. 窗口 {window_handle[:8]}...")
                print(f"   标题: {title}")
                print(f"   URL: {url}")
                print("-" * 60)
            except Exception as e:
                print(f"   ❌ 无法访问窗口: {str(e)}")
        
        # 切换回原窗口
        driver.switch_to.window(current_window)
        
    except Exception as e:
        print(f"❌ 检查窗口时出错: {str(e)}")


def interactive_test():
    """交互式测试"""
    try:
        print("🚀 Zealy.io 登录测试脚本")
        print("=" * 60)
        
        # 1. 列出可用的配置文件
        profiles = list_available_profiles()
        if not profiles:
            print("❌ 没有可用的配置文件")
            return
        
        # 2. 选择配置文件
        while True:
            try:
                choice = input(f"\n请选择配置文件 (1-{len(profiles)}) 或输入 'q' 退出: ").strip()
                if choice.lower() == 'q':
                    return
                
                choice_num = int(choice)
                if 1 <= choice_num <= len(profiles):
                    selected_profile = profiles[choice_num - 1]
                    # 尝试不同的ID字段名
                    profile_id = selected_profile.get('id') or selected_profile.get('profile_id') or selected_profile.get('profileId')
                    profile_name = selected_profile.get('name', '未命名')

                    if not profile_id:
                        print(f"❌ 配置文件缺少ID字段，可用字段: {list(selected_profile.keys())}")
                        continue

                    break
                else:
                    print(f"❌ 请输入 1-{len(profiles)} 之间的数字")
            except ValueError:
                print("❌ 请输入有效的数字")
        
        print(f"\n✅ 选择了配置文件: {profile_name} (ID: {profile_id})")
        
        # 3. 打开浏览器
        driver = open_browser_by_profile_id(profile_id)
        if not driver:
            print("❌ 浏览器打开失败")
            return
        
        try:
            # 4. 访问 Zealy.io 并检查登录状态
            navigation_result = navigate_to_zealy(driver)

            if navigation_result == "already_logged_in":
                print("🎉 账户已经登录，无需进行登录操作")
                print("✅ 测试完成 - 账户处于登录状态")

                # 等待用户确认
                input("\n按回车键继续...")
                return
            elif not navigation_result:
                print("❌ 访问 Zealy.io 失败")
                return

            # 5. 分析页面元素（只有在未登录时才执行）
            login_elements = find_and_analyze_login_elements(driver)
            
            if login_elements:
                # 6. 选择登录元素
                while True:
                    try:
                        choice = input(f"\n请选择要点击的登录元素 (1-{len(login_elements)}) 或输入 's' 跳过: ").strip()
                        if choice.lower() == 's':
                            break
                        
                        choice_num = int(choice)
                        if 1 <= choice_num <= len(login_elements):
                            selected_element = login_elements[choice_num - 1]
                            
                            # 尝试点击
                            if try_click_login_element(driver, selected_element):
                                # 7. 查找钱包选项
                                wallet_options = find_wallet_options(driver)
                                
                                if wallet_options:
                                    # 选择钱包选项
                                    while True:
                                        try:
                                            wallet_choice = input(f"\n请选择钱包选项 (1-{len(wallet_options)}) 或输入 's' 跳过: ").strip()
                                            if wallet_choice.lower() == 's':
                                                break
                                            
                                            wallet_num = int(wallet_choice)
                                            if 1 <= wallet_num <= len(wallet_options):
                                                selected_wallet = wallet_options[wallet_num - 1]
                                                
                                                # 点击钱包选项
                                                try:
                                                    wallet_element = selected_wallet['element']
                                                    driver.execute_script("arguments[0].scrollIntoView(true);", wallet_element)
                                                    time.sleep(1)
                                                    wallet_element.click()
                                                    print(f"✅ 成功点击钱包选项: {selected_wallet['text']}")
                                                    time.sleep(5)
                                                    
                                                    # 检查窗口变化
                                                    check_window_handles(driver)
                                                    
                                                except Exception as e:
                                                    print(f"❌ 点击钱包选项失败: {str(e)}")
                                                
                                                break
                                            else:
                                                print(f"❌ 请输入 1-{len(wallet_options)} 之间的数字")
                                        except ValueError:
                                            print("❌ 请输入有效的数字")
                            break
                        else:
                            print(f"❌ 请输入 1-{len(login_elements)} 之间的数字")
                    except ValueError:
                        print("❌ 请输入有效的数字")
            
            # 8. 等待用户操作
            input("\n按回车键继续...")
            
        finally:
            # 清理资源
            try:
                driver.quit()
                print("🔒 浏览器已关闭")
            except:
                pass
            
            try:
                close_browser_by_profile_id(profile_id)
            except:
                pass
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {str(e)}")
        import traceback
        print(f"🔍 详细错误信息: {traceback.format_exc()}")


if __name__ == "__main__":
    interactive_test()
