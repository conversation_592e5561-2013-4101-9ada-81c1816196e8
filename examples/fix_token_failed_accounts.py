#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复Token失效账户脚本
从email_extraction.log读取token失效的账户，为其分配新token并更新记录
"""

import json
import os
import sys
import time
import re
import httpx
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# 导入主脚本的函数
from examples.twitter_email_extractor import (
    get_profile_list,
    login_twitter_with_token,
    extract_email_from_settings,
    log_email_result,
    switch_ip,
    connect_mysql
)
from ixbrowser_local_api import IXBrowserClient

#用来根据日志，更换推特账号没有登录上去的浏览器环境

def read_failed_token_accounts(log_file='email_extraction.log'):
    """从日志文件中读取token失效的账户"""
    failed_accounts = []
    
    try:
        if not os.path.exists(log_file):
            print(f"❌ 日志文件不存在: {log_file}")
            return failed_accounts
        
        print(f"📋 读取日志文件: {log_file}")
        
        with open(log_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        for line in lines:
            line = line.strip()
            if 'token失效未登录' in line:
                # 解析日志行格式: [时间戳] 钱包: 钱包地址 | 邮箱: 邮箱地址 | 状态: 失败 | 原因: token失效未登录
                # 提取钱包地址
                import re
                wallet_match = re.search(r'钱包: (0x[a-fA-F0-9]{40})', line)
                if wallet_match:
                    wallet_address = wallet_match.group(1)
                    # 去重：只添加不重复的钱包地址
                    if wallet_address not in failed_accounts:
                        failed_accounts.append(wallet_address)
                        print(f"🔍 找到token失效账户: {wallet_address}")
                    else:
                        print(f"⚠️ 跳过重复的钱包地址: {wallet_address}")

        print(f"📊 共找到 {len(failed_accounts)} 个token失效账户（已去重）")
        return failed_accounts
        
    except Exception as e:
        print(f"❌ 读取日志文件失败: {str(e)}")
        return failed_accounts


def read_twitter_tokens(token_file='examples/twitter.txt'):
    """读取可用的Twitter token"""
    try:
        if not os.path.exists(token_file):
            print(f"❌ Token文件不存在: {token_file}")
            return []
        
        tokens = []
        with open(token_file, 'r', encoding='utf-8') as f:
            for line in f:
                token = line.strip()
                if token:  # 忽略空行
                    tokens.append(token)
        
        print(f"📋 从 {token_file} 读取到 {len(tokens)} 个token")
        return tokens
        
    except Exception as e:
        print(f"❌ 读取token文件失败: {str(e)}")
        return []


def save_twitter_tokens(tokens, token_file='examples/twitter.txt'):
    """保存Twitter token到文件"""
    try:
        with open(token_file, 'w', encoding='utf-8') as f:
            for token in tokens:
                f.write(token + '\n')
        print(f"💾 已保存 {len(tokens)} 个token到 {token_file}")
        return True
    except Exception as e:
        print(f"❌ 保存token文件失败: {str(e)}")
        return False


def remove_used_token_from_file(used_token, token_file='examples/twitter.txt'):
    """从token文件中删除已使用的token"""
    try:
        # 读取当前所有token
        current_tokens = read_twitter_tokens(token_file)

        # 删除已使用的token
        if used_token in current_tokens:
            current_tokens.remove(used_token)
            print(f"🗑️ 从文件中删除已使用的token: {used_token[:20]}...")

            # 保存更新后的token列表
            save_twitter_tokens(current_tokens, token_file)
            return True
        else:
            print(f"⚠️ 要删除的token不在文件中: {used_token[:20]}...")
            return False

    except Exception as e:
        print(f"❌ 删除token时出错: {str(e)}")
        return False


def read_accounts_data(accounts_file='examples/accounts.txt'):
    """读取accounts.txt文件"""
    try:
        if not os.path.exists(accounts_file):
            print(f"❌ 账户文件不存在: {accounts_file}")
            return []
        
        accounts_data = []
        with open(accounts_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line:
                    # 格式: email,password,wallet_address,private_key,discord_token,twitter_token,new_email,new_email_password
                    parts = line.split(',')
                    if len(parts) >= 8:
                        email = parts[0].strip()
                        password = parts[1].strip()
                        wallet_address = parts[2].strip()
                        private_key = parts[3].strip()
                        discord_token = parts[4].strip()
                        twitter_token = parts[5].strip()
                        new_email = parts[6].strip()
                        new_email_password = parts[7].strip()

                        accounts_data.append({
                            'email': email,
                            'password': password,
                            'wallet_address': wallet_address,
                            'private_key': private_key,
                            'discord_token': discord_token,
                            'twitter_token': twitter_token,
                            'new_email': new_email,
                            'new_email_password': new_email_password
                        })
        
        print(f"📋 从 {accounts_file} 读取到 {len(accounts_data)} 个账户")
        return accounts_data
        
    except Exception as e:
        print(f"❌ 读取账户文件失败: {str(e)}")
        return []


def save_accounts_data(accounts_data, accounts_file='examples/accounts.txt'):
    """保存accounts.txt文件"""
    try:
        with open(accounts_file, 'w', encoding='utf-8') as f:
            for account in accounts_data:
                line = f"{account['email']},{account['password']},{account['wallet_address']},{account['private_key']},{account['discord_token']},{account['twitter_token']},{account['new_email']},{account['new_email_password']}\n"
                f.write(line)
        
        print(f"💾 已保存 {len(accounts_data)} 个账户到 {accounts_file}")
        return True
        
    except Exception as e:
        print(f"❌ 保存账户文件失败: {str(e)}")
        return False


def extract_twitter_username(driver):
    """提取推特用户名"""
    try:
        print(f"🔍 尝试提取推特用户名...")

        # 访问推特个人资料页面
        driver.get("https://twitter.com/settings/profile")
        time.sleep(10)

        # 尝试多种选择器来获取用户名
        username_selectors = [
            "//input[@name='username']",
            "//input[@data-testid='ocfSettingsListUsername']",
            "//input[contains(@placeholder, 'username')]",
            "//input[contains(@aria-label, 'username')]",
            "//span[contains(@class, 'css-1jxf684')]//span[starts-with(text(), '@')]"
        ]

        for selector in username_selectors:
            try:
                print(f"🔍 尝试用户名选择器: {selector}")

                if selector.startswith("//span"):
                    # 对于span元素，获取文本内容
                    element = driver.find_element(By.XPATH, selector)
                    username_text = element.text.strip()
                    if username_text.startswith('@'):
                        username = username_text[1:]  # 去掉@符号
                        print(f"✅ 成功提取用户名: {username}")
                        return username
                else:
                    # 对于input元素，获取value属性
                    element = driver.find_element(By.XPATH, selector)
                    username = element.get_attribute('value').strip()
                    if username:
                        print(f"✅ 成功提取用户名: {username}")
                        return username

            except Exception as e:
                print(f"⚠️ 选择器 {selector} 失败: {str(e)}")
                continue

        # 如果上述方法都失败，尝试从URL中提取
        try:
            driver.get("https://twitter.com/home")
            time.sleep(5)

            # 查找个人资料链接
            profile_links = driver.find_elements(By.XPATH, "//a[contains(@href, '/') and contains(@aria-label, 'Profile')]")
            for link in profile_links:
                href = link.get_attribute('href')
                if href and '/status/' not in href and '/search' not in href:
                    username = href.split('/')[-1]
                    if username and username != 'home' and username != 'notifications':
                        print(f"✅ 从链接提取用户名: {username}")
                        return username
        except Exception as e:
            print(f"⚠️ 从链接提取用户名失败: {str(e)}")

        print(f"❌ 无法提取推特用户名")
        return None

    except Exception as e:
        print(f"❌ 提取用户名时出错: {str(e)}")
        return None


def query_username_password(username):
    """根据用户名查询密码（模糊匹配）"""
    connection = None
    try:
        print(f"🔍 根据用户名查询密码: {username}")

        connection = connect_mysql()
        if not connection:
            return None

        cursor = connection.cursor()

        # 使用LIKE进行模糊查询，匹配包含用户名的URL
        query = "SELECT password FROM `acc-twitter` WHERE username LIKE %s"
        search_pattern = f"%{username}%"
        cursor.execute(query, (search_pattern,))

        result = cursor.fetchone()

        if result:
            password = result[0]
            print(f"✅ 根据用户名找到匹配的密码: {password[:3]}***{password[-3:] if len(password) > 6 else '***'}")
            return password
        else:
            print(f"❌ 未找到用户名 {username} 对应的密码")
            return None

    except Exception as e:
        print(f"❌ 根据用户名查询数据库时出错: {str(e)}")
        return None
    finally:
        if connection:
            connection.close()


def check_twitter_login_status(driver):
    """检查推特登录状态"""
    try:
        print(f"🔍 检查推特登录状态...")

        # 访问推特邮箱设置页面
        driver.get("https://twitter.com/settings/your_twitter_data/account")

        # 增加等待时间，让浏览器完全加载
        print(f"⏳ 等待浏览器完全加载（15秒）...")
        time.sleep(15)

        # 检查是否需要登录
        current_url = driver.current_url
        print(f"📍 当前页面URL: {current_url}")

        # 如果URL包含login，说明需要登录
        if 'login' in current_url.lower() or 'oauth' in current_url.lower():
            print(f"❌ 推特未登录，需要使用新token登录")
            return False

        # 再等待一段时间确保页面元素完全加载
        print(f"⏳ 等待页面元素加载完成（10秒）...")
        time.sleep(10)

        # 尝试提取邮箱来验证登录状态
        extracted_email = extract_email_from_settings(driver)
        if extracted_email:
            print(f"✅ 推特已登录，当前邮箱: {extracted_email}")
            return True, extracted_email, None
        else:
            print(f"❌ 无法提取邮箱，尝试提取用户名...")

            # 尝试提取用户名
            username = extract_twitter_username(driver)
            if username:
                print(f"✅ 推特已登录，当前用户名: {username}")
                return True, None, username
            else:
                print(f"❌ 无法提取邮箱和用户名，可能未登录或页面加载失败")
                return False, None, None

    except Exception as e:
        print(f"❌ 检查登录状态时出错: {str(e)}")
        return False


def query_password_by_email_or_username(email, username):
    """根据邮箱或用户名查询密码"""
    try:
        # 首先尝试根据邮箱查询
        if email:
            from examples.twitter_email_extractor import query_email_password
            password = query_email_password(email)
            if password:
                return password

        # 如果邮箱查询失败，尝试根据用户名查询
        if username:
            password = query_username_password(username)
            if password:
                return password

        print(f"❌ 根据邮箱和用户名都无法找到密码")
        return None

    except Exception as e:
        print(f"❌ 查询密码时出错: {str(e)}")
        return None


def update_account_token(accounts_data, wallet_address, new_token):
    """更新指定钱包地址的Twitter token"""
    for account in accounts_data:
        if account['wallet_address'] == wallet_address:
            old_token = account['twitter_token']
            account['twitter_token'] = new_token
            print(f"✅ 更新账户 {wallet_address} 的token")
            print(f"   旧token: {old_token[:20]}...")
            print(f"   新token: {new_token[:20]}...")
            return True
    
    print(f"❌ 未找到钱包地址: {wallet_address}")
    return False


def fix_single_account(client, wallet_address, new_token):
    """修复单个账户的token

    Returns:
        tuple: (是否成功, 是否使用了新token)
    """
    try:
        print(f"\n🔧 开始修复账户: {wallet_address}")
        print(f"🎯 使用新token: {new_token[:20]}...")
        
        # 1. 查找配置文件
        profiles, error_code, error_message = get_profile_list(wallet_address)

        if not profiles:
            print(f"❌ 未找到配置文件: {wallet_address}")
            if error_code:
                print(f"   错误代码: {error_code}")
                print(f"   错误信息: {error_message}")
            return False, False  # 失败，未使用新token

        profile_id = profiles[0].get('profile_id')
        print(f"✅ 找到配置文件: {wallet_address} (ID: {profile_id})")
        
        # 2. 打开浏览器
        print(f"🚀 正在打开浏览器...")
        try:
            open_result = client.open_profile(profile_id, load_profile_info_page=False)

            if not open_result:
                print(f"❌ 打开浏览器失败: {client.code} - {client.message}")
                return False, False  # 失败，未使用新token

            print(f"✅ 浏览器打开成功")

        except Exception as e:
            print(f"❌ 打开浏览器时出错: {str(e)}")
            return False, False  # 失败，未使用新token

        # 3. 获取调试地址并连接Selenium
        debug_address = open_result.get('debugging_address', '')
        if debug_address.startswith('http://'):
            debug_address = debug_address[7:]
        elif debug_address.startswith('https://'):
            debug_address = debug_address[8:]

        chrome_options = Options()
        chrome_options.debugger_address = debug_address

        driver = None
        try:
            # 初始化WebDriver
            webdriver_path = open_result.get('webdriver', '')
            if webdriver_path:
                service = webdriver.chrome.service.Service(executable_path=webdriver_path)
                driver = webdriver.Chrome(service=service, options=chrome_options)
            else:
                driver = webdriver.Chrome(options=chrome_options)

            print("✅ WebDriver连接成功")

        except Exception as e:
            print(f"❌ WebDriver连接失败: {str(e)}")
            # 关闭浏览器
            try:
                client.close_profile(profile_id)
            except:
                pass
            return False, False  # 失败，未使用新token

        # 4. 检查推特登录状态
        is_logged_in, extracted_email, extracted_username = check_twitter_login_status(driver)

        if is_logged_in:
            print(f"✅ 推特已登录，无需使用新token")

            # 根据提取到的信息记录结果
            if extracted_email:
                print(f"✅ 登录验证成功，提取到邮箱: {extracted_email}")
                log_email_result(wallet_address, extracted_email, True, "已登录，无需修复token")
            elif extracted_username:
                print(f"✅ 登录验证成功，提取到用户名: {extracted_username}")
                log_email_result(wallet_address, f"@{extracted_username}", True, "已登录，无需修复token")
            else:
                print(f"✅ 登录验证成功，但无法提取邮箱或用户名")
                log_email_result(wallet_address, "unknown", True, "已登录，无需修复token")

            # 关闭浏览器
            driver.quit()
            try:
                close_result = client.close_profile(profile_id)
                if close_result:
                    print("✅ 浏览器已关闭")
            except Exception as e:
                print(f"⚠️ 关闭浏览器时出错: {str(e)}")

            return True, False  # 成功，但未使用新token
        else:
            print(f"❌ 推特未登录，继续使用新token登录")

        # 5. 使用新token登录Twitter
        print(f"🔑 开始使用新token登录Twitter...")
        login_success = login_twitter_with_token(driver, new_token)
        
        if not login_success:
            print(f"❌ 新token登录失败")
            driver.quit()
            try:
                client.close_profile(profile_id)
            except:
                pass
            return False, False  # 失败，未成功使用新token
        
        print(f"✅ 新token登录成功")

        # 6. 验证登录状态 - 尝试提取邮箱
        print(f"🔍 验证登录状态...")
        extracted_email = extract_email_from_settings(driver)
        
        if extracted_email:
            print(f"✅ 登录验证成功，提取到邮箱: {extracted_email}")

            # 记录成功结果
            log_email_result(wallet_address, extracted_email, True, "token修复成功")

            # 关闭浏览器
            driver.quit()
            try:
                close_result = client.close_profile(profile_id)
                if close_result:
                    print("✅ 浏览器已关闭")
            except Exception as e:
                print(f"⚠️ 关闭浏览器时出错: {str(e)}")

            return True, True  # 成功，并且使用了新token
        else:
            print(f"❌ 无法提取邮箱，尝试提取用户名...")

            # 尝试提取用户名
            username = extract_twitter_username(driver)
            if username:
                print(f"✅ 登录验证成功，提取到用户名: {username}")

                # 记录成功结果
                log_email_result(wallet_address, f"@{username}", True, "token修复成功（通过用户名）")

                # 关闭浏览器
                driver.quit()
                try:
                    close_result = client.close_profile(profile_id)
                    if close_result:
                        print("✅ 浏览器已关闭")
                except Exception as e:
                    print(f"⚠️ 关闭浏览器时出错: {str(e)}")

                return True, True  # 成功，并且使用了新token
            else:
                print(f"❌ 登录验证失败，无法提取邮箱和用户名")

                # 记录失败结果
                log_email_result(wallet_address, None, False, "token修复后仍无法提取邮箱和用户名")

                # 关闭浏览器
                driver.quit()
                try:
                    client.close_profile(profile_id)
                except:
                    pass
                return False, True  # 失败，但已使用了新token
            
    except Exception as e:
        print(f"❌ 修复账户时出错: {str(e)}")
        
        # 确保关闭浏览器
        try:
            if 'driver' in locals():
                driver.quit()
            if 'profile_id' in locals():
                client.close_profile(profile_id)
        except:
            pass
        
        return False, False  # 失败，未使用新token


def main():
    """主函数"""
    print("🔧 Token失效账户修复脚本启动")
    print("=" * 50)
    
    # 1. 读取token失效的账户
    failed_accounts = read_failed_token_accounts()
    
    if not failed_accounts:
        print("✅ 没有需要修复的token失效账户")
        return
    
    # 2. 读取可用的Twitter token
    available_tokens = read_twitter_tokens()
    
    if not available_tokens:
        print("❌ 没有可用的Twitter token")
        return
    
    if len(available_tokens) < len(failed_accounts):
        print(f"⚠️ 可用token数量({len(available_tokens)})少于失效账户数量({len(failed_accounts)})")
        print(f"   将只修复前 {len(available_tokens)} 个账户")
    
    # 3. 读取accounts.txt数据
    accounts_data = read_accounts_data()
    
    if not accounts_data:
        print("❌ 无法读取accounts.txt文件")
        return
    
    # 4. 初始化浏览器客户端
    client = IXBrowserClient()
    
    # 5. 处理每个失效账户
    success_count = 0
    failed_count = 0

    # 处理所有失效账户
    process_limit = min(len(failed_accounts), len(available_tokens))
    print(f"🚀 生产模式：处理所有 {process_limit} 个账户")

    for i, wallet_address in enumerate(failed_accounts[:process_limit]):
        if i >= len(available_tokens):
            print(f"⚠️ token不足，停止处理剩余账户")
            break
        
        print(f"\n{'='*20} 修复第 {i+1}/{process_limit} 个账户 {'='*20}")
        print(f"钱包地址: {wallet_address}")
        
        # 取出一个新token
        new_token = available_tokens.pop(0)
        
        # 修复账户
        fix_result, used_new_token = fix_single_account(client, wallet_address, new_token)

        if fix_result:
            success_count += 1
            print(f"✅ 账户 {wallet_address} 修复成功")

            # 只有在实际使用了新token的情况下才更新accounts.txt和删除token
            if used_new_token:
                # 更新accounts.txt中的token记录
                if update_account_token(accounts_data, wallet_address, new_token):
                    print(f"✅ 已更新accounts.txt中的token记录")
                else:
                    print(f"⚠️ 未能更新accounts.txt中的token记录")

                # 从twitter.txt文件中删除已使用的token
                remove_used_token_from_file(new_token)
            else:
                print(f"💡 账户已登录，无需更新token记录")
                # 将未使用的token放回列表
                available_tokens.append(new_token)
        else:
            failed_count += 1
            print(f"❌ 账户 {wallet_address} 修复失败")

            # 如果使用了新token但失败了，仍然要从文件中删除（token可能已失效）
            if used_new_token:
                print(f"🗑️ 删除失效的token...")
                remove_used_token_from_file(new_token)
            else:
                # 如果没有使用新token就失败了，将token放回列表
                available_tokens.append(new_token)
        
        # 等待一段时间并切换IP再处理下一个账户
        if i < process_limit - 1:
            print(f"⏳ 等待60秒后处理下一个账户...")
            time.sleep(60)

            # 切换IP
            print(f"🌐 准备为下一个账户切换IP...")
            ip_switch_success = switch_ip()
            if ip_switch_success:
                print("✅ IP切换成功")
            else:
                print("⚠️ IP切换失败，但继续处理下一个账户...")
    
    # 6. 保存更新后的文件
    print(f"\n💾 保存更新后的文件...")
    
    # 保存剩余的token
    if save_twitter_tokens(available_tokens):
        print(f"✅ 已保存剩余的 {len(available_tokens)} 个token")
    
    # 保存更新后的accounts.txt
    if save_accounts_data(accounts_data):
        print(f"✅ 已保存更新后的accounts.txt")
    
    # 7. 显示统计结果
    print(f"\n📊 修复结果统计:")
    print(f"   ✅ 成功修复: {success_count} 个账户")
    print(f"   ❌ 修复失败: {failed_count} 个账户")
    print(f"   🎯 剩余可用token: {len(available_tokens)} 个")
    print("🎉 Token修复脚本执行完成！")


if __name__ == '__main__':
    main()
