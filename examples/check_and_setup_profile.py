import json
import os
import sys
import time

import httpx
from ixbrowser_local_api import IXBrowserClient
from auto_profile_setup import (
    read_data_file, 
    create_profile_with_settings, 
    setup_browser_automation, 
    handle_failed_profile
)

#根据账号信息批量的检测是否存在这个浏览器，如果不存在就创建浏览器，然后导入钱包，discord和推特

def check_profile_exists(client, wallet_address):
    """检查是否存在以wallet_address命名的浏览器配置文件"""
    try:
        # 首先尝试直接使用name参数查询
        print(f"正在查询名称为 '{wallet_address}' 的配置文件...")
        
        # 获取所有配置文件列表
        profiles = client.get_profile_list()
        if profiles is None:
            print(f"获取配置文件列表失败: {client.code} - {client.message}")
            return None
        
        # 打印所有配置文件名称，用于调试
        print(f"当前系统中的配置文件列表:")
        for p in profiles:
            print(f"  ID: {p.get('profile_id')}, 名称: {p.get('name')}")
        
        # 查找匹配wallet_address的配置文件（不区分大小写）
        wallet_address_lower = wallet_address.lower()
        for profile in profiles:
            profile_name = profile.get('name', '')
            # 检查完全匹配
            if profile_name == wallet_address:
                print(f"找到完全匹配的配置文件: {profile['profile_id']} - {profile['name']}")
                return profile['profile_id']
            # 检查不区分大小写的匹配
            elif profile_name.lower() == wallet_address_lower:
                print(f"找到不区分大小写匹配的配置文件: {profile['profile_id']} - {profile['name']}")
                return profile['profile_id']
            # 检查是否包含该地址（有时可能带有前缀或后缀）
            elif wallet_address in profile_name or wallet_address_lower in profile_name.lower():
                print(f"找到包含该地址的配置文件: {profile['profile_id']} - {profile['name']}")
                return profile['profile_id']
            # 检查配置文件名是否包含在钱包地址中（反向包含）
            elif profile_name and (profile_name.lower() in wallet_address_lower):
                print(f"找到钱包地址包含配置文件名的情况: {profile['profile_id']} - {profile['name']}")
                return profile['profile_id']
        
        # 未找到匹配的配置文件
        print(f"未找到名为或包含 {wallet_address} 的配置文件")
        return None
    except Exception as e:
        print(f"检查配置文件时出错: {str(e)}")
        return None
def get_profile_list(name=None, timeout=20.0):
    """
    获取配置文件列表的独立函数

    Args:
        name (str, optional): 配置文件名称，用于过滤
        timeout (float): 请求超时时间，默认20秒

    Returns:
        tuple: (profiles_list, error_code, error_message)
               - profiles_list: 配置文件列表，失败时为None
               - error_code: 错误代码，成功时为None
               - error_message: 错误消息，成功时为None
    """
    client = None
    try:
        # 创建HTTP客户端
        client = httpx.Client(timeout=timeout)

        url = "http://localhost:53200/api/v2/profile-list"
        params = {
            "page": 1,
            "limit": 1  # 获取更多结果
        }

        # 如果提供了名称，添加到查询参数中
        if name:
            params["name"] = name

        print(f"调试信息 - 请求URL: {url}")
        print(f"调试信息 - 请求参数: {json.dumps(params, indent=2)}")

        response = client.post(url, json=params)  # 使用POST方法，传递JSON数据
        print(f"\n调试信息 - API响应状态码: {response.status_code}")
        print(f"调试信息 - 响应内容: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")

        data = response.json()
        if response.status_code == 200 and data.get("error", {}).get("code") == 0:
            profiles = data.get("data", {}).get("data", [])
            print(f"调试信息 - 获取到的配置文件数量: {len(profiles)}")
            return profiles, None, None

        error_code = data.get("error", {}).get("code")
        error_message = data.get("error", {}).get("message")
        return None, error_code, error_message

    except Exception as e:
        error_message = str(e)
        print(f"调试信息 - 发生异常: {error_message}")
        return None, None, error_message
    finally:
        # 确保客户端被正确关闭
        if client:
            client.close()

def main():
    # 初始化客户端
    client = IXBrowserClient()

    # 配置参数
    data_file = 'accounts.txt'
    http_proxy = 'ip.mproxy.vn:12248@cashbag:cWt6bAQdTeapT'
    group_id = 149546
    tag_id = "zearly"

    # 读取资料文件
    profiles_data = read_data_file(data_file)
    if not profiles_data:
        print("没有读取到有效的资料数据")
        return

    # 为每个资料检查并创建profile
    for i, data in enumerate(profiles_data, 1):
        wallet_address = data['wallet_address']
        
        # 检查是否存在以wallet_address命名的配置文件
        profiles, error_code, error_message = get_profile_list(wallet_address)

        # 检查是否找到匹配的配置文件
        profile_id = None
        if profiles:
            # 如果找到配置文件，取第一个的ID
            profile_id = profiles[0].get('profile_id') if len(profiles) > 0 else None

        if profile_id:
            print(f"配置文件 {wallet_address} 已存在，跳过创建")
            continue
        else:
            print(f"配置文件 {wallet_address} 不存在，开始创建...")
            # 创建新的配置文件
            profile_id = create_profile_with_settings(client, data, http_proxy, group_id, tag_id)
            if profile_id:
                if not setup_browser_automation(client, profile_id, data):
                    handle_failed_profile(data)
            else:
                handle_failed_profile(data)

        # 如果不是最后一个账号，则切换IP
        if i < len(profiles_data):
            print("\n准备切换IP...")
            try:
                with httpx.Client(timeout=30.0) as http_client:
                    response = http_client.get(
                        "https://mproxy.vn/capi/_iCp27tGi46qKNBKo0TyUArkI-8rPGrkK7BB83SMAts/key/cWt6bAQdTeapT/resetIp"
                    )
                    if response.status_code == 200:
                        print("✅ IP切换成功")
                        time.sleep(10)  # 等待10秒让IP生效
                    else:
                        print(f"❌ IP切换失败，状态码: {response.status_code}")
            except Exception as e:
                print(f"❌ IP切换请求出错: {str(e)}")

if __name__ == '__main__':
    main()