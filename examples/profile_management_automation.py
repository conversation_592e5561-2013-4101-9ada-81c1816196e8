import os
import time
import json
import httpx
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common import NoSuchElementException, ElementClickInterceptedException

from examples.auto_profile_setup import setup_browser_automation
from ixbrowser_local_api import IXBrowserClient, Profile, Proxy, Preference, Fingerprint

def read_data_file(file_path):
    """读取资料文件，返回解析后的数据列表"""
    profiles = []
    with open(file_path, 'r') as f:
        for line in f:
            parts = line.strip().split(',')
            if len(parts) == 8:  # 确保有8个字段
                profiles.append({
                    'email': parts[0],
                    'email_password': parts[1],
                    'wallet_address': parts[2],
                    'wallet_key': parts[3],
                    'discord_token': parts[4],
                    'twitter_token': parts[5],
                    'new_email': parts[6],
                    'new_email_password': parts[7]
                })
    return profiles

def check_profile_exists(client, wallet_address):
    """检查是否存在以wallet_address命名的浏览器配置文件"""
    try:
        print(f"正在查询配置文件: {wallet_address}")
        profiles = client.get_profile_list(name=wallet_address)
        
        if profiles is None:
            print(f"获取配置文件列表失败: {client.code} - {client.message}")
            return None
            
        if profiles:
            for profile in profiles:
                if profile.get('name', '').lower() == wallet_address.lower():
                    print(f"找到匹配的配置文件 ID: {profile['profile_id']}")
                    return profile['profile_id']
                    
        print(f"未找到匹配的配置文件")
        return None
    except Exception as e:
        print(f"检查配置文件时出错: {str(e)}")
        return None

def create_profile_with_settings(client, profile_data, http_proxy, group_id, tag_id):
    """创建新的浏览器配置文件"""
    try:
        # 解析代理信息
        proxy_parts = http_proxy.split('@')
        if len(proxy_parts) == 2:
            ip_port = proxy_parts[0].split(':')
            auth = proxy_parts[1].split(':')
            proxy_ip = ip_port[0]
            proxy_port = int(ip_port[1])
            username = auth[0]
            password = auth[1]
        else:
            print("代理格式错误")
            return None

        # 创建配置
        profile = Profile()
        profile.name = profile_data['wallet_address']
        profile.group_id = group_id
        profile.tag = tag_id

        # 设置代理
        proxy = Proxy()
        proxy.change_to_custom_mode('socks5', proxy_ip, proxy_port, username, password)
        profile.proxy_config = proxy

        # 设置指纹和备份配置
        fingerprint = Fingerprint()
        fingerprint.language = 'en-US'
        profile.fingerprint_config = fingerprint

        preference = Preference()
        preference.set_cloud_backup(1, 1, 1)
        profile.preference_config = preference

        # 创建配置文件
        profile_id = client.create_profile(profile)
        if not profile_id:
            print(f"创建配置文件失败: {client.code} - {client.message}")
            return None

        print(f"成功创建配置文件: {profile_id}")
        return profile_id

    except Exception as e:
        print(f"创建配置文件时出错: {str(e)}")
        return None

def handle_failed_profile(data, output_file='failed_profiles.txt'):
    """记录失败的配置文件信息"""
    line = ','.join([
        data['email'],
        data['email_password'],
        data['wallet_address'],
        data['wallet_key'],
        data['discord_token'],
        data['twitter_token'],
        data['new_email'],
        data['new_email_password']
    ])
    with open(output_file, 'a', encoding='utf-8') as f:
        f.write(line + '\n')
    print(f"已将失败的配置保存到 {output_file}")

def main():
    # 初始化客户端
    client = IXBrowserClient()

    # 配置参数
    data_file = 'accounts.txt'
    http_proxy = 'ip.mproxy.vn:12248@cashbag:cWt6bAQdTeapT'
    group_id = 149546
    tag_id = "zearly"

    # 读取数据文件
    profiles_data = read_data_file(data_file)
    if not profiles_data:
        print("没有读取到有效的数据")
        return

    # 处理每个配置
    for i, data in enumerate(profiles_data, 1):
        wallet_address = data['wallet_address']
        
        # 检查配置文件是否存在
        profile_id = check_profile_exists(client, wallet_address)
        
        if profile_id:
            print(f"配置文件 {wallet_address} 已存在，跳过处理")
            continue
            
        print(f"配置文件 {wallet_address} 不存在，开始创建...")
        
        # 创建新配置文件
        profile_id = create_profile_with_settings(client, data, http_proxy, group_id, tag_id)
        if profile_id:
            # 设置浏览器自动化
            if not setup_browser_automation(client, profile_id, data):
                print(f"浏览器自动化设置失败，记录到失败列表")
                handle_failed_profile(data)
        else:
            print(f"创建配置文件失败，记录到失败列表")
            handle_failed_profile(data)

        # 如果不是最后一个配置，切换IP
        if i < len(profiles_data):
            print("\n准备切换IP...")
            try:
                with httpx.Client(timeout=30.0) as http_client:
                    response = http_client.get(
                        "https://mproxy.vn/capi/_iCp27tGi46qKNBKo0TyUArkI-8rPGrkK7BB83SMAts/key/cWt6bAQdTeapT/resetIp"
                    )
                    if response.status_code == 200:
                        print("✅ IP切换成功")
                        time.sleep(10)  # 等待IP生效
                    else:
                        print(f"❌ IP切换失败，状态码: {response.status_code}")
            except Exception as e:
                print(f"❌ IP切换请求出错: {str(e)}")

if __name__ == '__main__':
    main()