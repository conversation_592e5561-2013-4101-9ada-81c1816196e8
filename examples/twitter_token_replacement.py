import json
import os
import sys
import time
import shutil

import httpx
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common import NoSuchElementException, TimeoutException

from ixbrowser_local_api import IXBrowser<PERSON>lient
from auto_profile_setup import read_data_file, handle_failed_profile

#根据账号信息批量的打开浏览器，检测推特是否存活或者在登录状态，如果不在登录状态就取一个新的token来登录
def switch_ip():
    """
    切换IP地址

    Returns:
        bool: 切换是否成功
    """
    try:
        print("🔄 正在切换IP地址...")
        with httpx.Client(timeout=30.0) as http_client:
            response = http_client.get(
                "https://mproxy.vn/capi/_iCp27tGi46qKNBKo0TyUArkI-8rPGrkK7BB83SMAts/key/cWt6bAQdTeapT/resetIp"
            )
            if response.status_code == 200:
                print(response)
                time.sleep(10)  # 等待10秒让IP生效
                return True
            else:
                print(f"❌ IP切换失败，状态码: {response.status_code}")
                return False
    except Exception as e:
        print(f"❌ IP切换请求出错: {str(e)}")
        return False


def switch_ip():
    """
    切换IP地址

    Returns:
        bool: 切换是否成功
    """
    try:
        print("🔄 正在切换IP地址...")
        with httpx.Client(timeout=30.0) as http_client:
            response = http_client.get(
                "https://mproxy.vn/capi/_iCp27tGi46qKNBKo0TyUArkI-8rPGrkK7BB83SMAts/key/cWt6bAQdTeapT/resetIp"
            )
            if response.status_code == 200:
                print(response)
                time.sleep(50) # 等待10秒让IP生效
                return True
            else:
                print(f"❌ IP切换失败，状态码: {response.status_code}")
                return False
    except Exception as e:
        print(f"❌ IP切换请求出错: {str(e)}")
        return False


def get_profile_list(name=None, timeout=20.0):
    """获取配置文件列表"""
    client = None
    try:
        client = httpx.Client(timeout=timeout)
        url = "http://localhost:53200/api/v2/profile-list"
        params = {"page": 1, "limit": 100}
        
        if name:
            params["name"] = name
            
        response = client.post(url, json=params)
        data = response.json()
        
        if response.status_code == 200 and data.get("error", {}).get("code") == 0:
            profiles = data.get("data", {}).get("data", [])
            return profiles, None, None
        
        error_code = data.get("error", {}).get("code")
        error_message = data.get("error", {}).get("message")
        return None, error_code, error_message
        
    except Exception as e:
        return None, None, str(e)
    finally:
        if client:
            client.close()


def read_twitter_tokens(token_file='twitter.txt'):
    """
    读取Twitter token文件
    
    Args:
        token_file: token文件路径
        
    Returns:
        list: token列表
    """
    tokens = []
    try:
        if os.path.exists(token_file):
            with open(token_file, 'r', encoding='utf-8') as f:
                for line in f:
                    token = line.strip()
                    if token:  # 忽略空行
                        tokens.append(token)
        print(f"📋 从 {token_file} 读取到 {len(tokens)} 个token")
        return tokens
    except Exception as e:
        print(f"❌ 读取token文件失败: {str(e)}")
        return []


def save_twitter_tokens(tokens, token_file='twitter.txt'):
    """
    保存Twitter token到文件

    Args:
        tokens: token列表
        token_file: token文件路径
    """
    try:
        with open(token_file, 'w', encoding='utf-8') as f:
            for token in tokens:
                f.write(token + '\n')
        print(f"💾 已保存 {len(tokens)} 个token到 {token_file}")
    except Exception as e:
        print(f"❌ 保存token文件失败: {str(e)}")


def remove_used_token_from_file(used_token, token_file='twitter.txt'):
    """
    实时从token文件中移除已使用的token

    Args:
        used_token: 已使用的token
        token_file: token文件路径

    Returns:
        bool: 移除是否成功
    """
    try:
        # 读取当前所有token
        current_tokens = read_twitter_tokens(token_file)

        # 移除已使用的token
        if used_token in current_tokens:
            current_tokens.remove(used_token)

            # 立即保存到文件
            save_twitter_tokens(current_tokens, token_file)
            print(f"🗑️ 已从文件中实时移除使用过的token: {used_token[:20]}...")
            return True
        else:
            print(f"⚠️ 要移除的token不在文件中: {used_token[:20]}...")
            return False

    except Exception as e:
        print(f"❌ 实时移除token时出错: {str(e)}")
        return False


def update_account_token_in_file(wallet_address, new_token, accounts_file='accounts.txt'):
    """
    实时更新accounts文件中指定账户的token

    Args:
        wallet_address: 钱包地址
        new_token: 新的token
        accounts_file: 账户文件路径

    Returns:
        bool: 更新是否成功
    """
    try:
        # 读取当前账户数据
        accounts_data = read_data_file(accounts_file)
        if not accounts_data:
            return False

        # 查找并更新指定账户的token
        updated = False
        for data in accounts_data:
            if data['wallet_address'] == wallet_address:
                old_token = data['twitter_token']
                data['twitter_token'] = new_token
                print(f"🔄 实时更新账户 {wallet_address} 的token")
                print(f"   旧token: {old_token[:20]}...")
                print(f"   新token: {new_token[:20]}...")
                updated = True
                break

        if updated:
            # 立即保存到文件
            update_accounts_file(accounts_data, accounts_file)
            return True
        else:
            print(f"⚠️ 未找到钱包地址为 {wallet_address} 的账户")
            return False

    except Exception as e:
        print(f"❌ 实时更新账户token时出错: {str(e)}")
        return False


def update_accounts_file(accounts_data, accounts_file='accounts.txt'):
    """
    更新accounts.txt文件
    
    Args:
        accounts_data: 账户数据列表
        accounts_file: 账户文件路径
    """
    try:
        # 备份原文件
        backup_file = f"{accounts_file}.backup"
        if os.path.exists(accounts_file):
            shutil.copy2(accounts_file, backup_file)
            print(f"📋 已备份原文件到 {backup_file}")
        
        # 写入新数据
        with open(accounts_file, 'w', encoding='utf-8') as f:
            for data in accounts_data:
                line = ','.join([
                    data['email'],
                    data['email_password'],
                    data['wallet_address'],
                    data['wallet_key'],
                    data['discord_token'],
                    data['twitter_token'],
                    data['new_email'],
                    data['new_email_password']
                ])
                f.write(line + '\n')
        print(f"💾 已更新 {accounts_file}")
    except Exception as e:
        print(f"❌ 更新账户文件失败: {str(e)}")


def wait_for_page_load(driver, max_wait_time=30):
    """
    等待页面完全加载

    Args:
        driver: WebDriver实例
        max_wait_time: 最大等待时间（秒）

    Returns:
        bool: 页面是否成功加载
    """
    try:
        print(f"⏳ 等待页面加载完成（最多{max_wait_time}秒）...")

        # 等待页面基本元素加载
        WebDriverWait(driver, max_wait_time).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )

        # 检查页面是否包含基本的Twitter元素
        start_time = time.time()
        while time.time() - start_time < max_wait_time:
            page_source = driver.page_source.lower()

            # 检查是否有Twitter的基本结构
            if any(keyword in page_source for keyword in ['twitter', 'x.com', 'tweet', 'timeline']):
                print("✅ 页面基本结构加载完成")
                time.sleep(3)  # 额外等待3秒确保动态内容加载
                return True

            # 检查是否有错误页面
            if any(keyword in page_source for keyword in ['error', 'not found', '404', '500', 'timeout']):
                print("❌ 页面加载出现错误")
                return False

            time.sleep(2)

        print("⚠️ 页面加载超时，但继续检测")
        return True

    except TimeoutException:
        print("❌ 页面加载超时")
        return False
    except Exception as e:
        print(f"❌ 等待页面加载时出错: {str(e)}")
        return False


def check_twitter_login_status(driver, retry_count=3):
    """
    检查Twitter是否处于登录状态（严格版本，减少误判）

    Args:
        driver: WebDriver实例
        retry_count: 重试次数

    Returns:
        bool: True表示已登录，False表示未登录
    """
    for attempt in range(retry_count):
        try:
            print(f"🔍 检查Twitter登录状态（第{attempt + 1}/{retry_count}次尝试）...")

            # 首先等待页面完全加载
            if not wait_for_page_load(driver):
                if attempt < retry_count - 1:
                    print("🔄 页面加载失败，刷新页面重试...")
                    driver.refresh()
                    continue
                else:
                    print("❌ 页面加载失败，无法检测登录状态")
                    return False

            # 获取当前URL和页面源码
            current_url = driver.current_url.lower()
            page_source = driver.page_source.lower()

            print(f"📍 当前URL: {current_url}")

            # 1. 首先检查明确的未登录标志（优先级最高）
            logout_indicators = [
                ("//span[contains(text(), 'Sign in to X')]", "登录到X的提示"),
                ("//span[contains(text(), 'Sign in')]", "登录按钮"),
                ("//span[contains(text(), 'Log in')]", "登录按钮"),
                ("//input[@name='text']", "用户名输入框"),
                ("//input[@name='password']", "密码输入框"),
                ("//div[contains(text(), \"Don't have an account?\")]", "注册提示"),
                ("//span[contains(text(), 'Create account')]", "创建账户按钮"),
                ("//div[contains(text(), 'Join X today')]", "加入X提示"),
                ("//button[contains(text(), 'Sign up')]", "注册按钮"),
                ("//div[@data-testid='loginButton']", "登录按钮"),
                ("//div[@data-testid='signupButton']", "注册按钮")
            ]

            for xpath, description in logout_indicators:
                elements = driver.find_elements(By.XPATH, xpath)
                if elements:
                    print(f"❌ Twitter未登录（发现{description}）")
                    return False

            # 2. 检查URL是否在登录相关页面
            login_url_keywords = ['login', 'signin', 'oauth', 'authenticate', 'welcome']
            if any(keyword in current_url for keyword in login_url_keywords):
                print("❌ Twitter未登录，当前在登录相关页面")
                return False

            # 3. 检查页面源码中的明确未登录标志
            logout_page_keywords = [
                'sign in to x', 'log in to twitter', 'create your account',
                'join x today', 'new to x', 'sign up for x', 'welcome to x',
                '"isLoggedIn":false', '"authenticated":false'
            ]

            for keyword in logout_page_keywords:
                if keyword in page_source:
                    print(f"❌ Twitter未登录（页面包含: {keyword}）")
                    return False

            # 3.5. 检查页面是否为空白或加载失败
            if len(page_source) < 10000:  # 正常Twitter页面应该很大
                print(f"❌ 页面内容过少（{len(page_source)}字符），可能加载失败")
                if attempt < retry_count - 1:
                    print("🔄 页面内容不足，刷新重试...")
                    driver.refresh()
                    time.sleep(5)
                    continue
                else:
                    return False

            # 4. 严格检查登录状态标志（必须有明确的登录标志）
            strict_login_indicators = [
                ("//a[@href='/compose/post']", "发推按钮"),
                ("//div[@data-testid='SideNav_AccountSwitcher_Button']", "用户菜单"),
                ("//a[@aria-label='Profile']", "个人资料链接"),
                ("//div[@data-testid='tweetTextarea_0']", "推文输入框"),
                ("//div[@data-testid='primaryColumn']//div[@data-testid='cellInnerDiv']", "时间线内容"),
                ("//nav[@role='navigation']//a[@href='/home']", "导航栏首页链接"),
                ("//div[@data-testid='sidebarColumn']", "侧边栏"),
                ("//div[@data-testid='AppTabBar_Home_Link']", "底部导航首页"),
                ("//div[@data-testid='AppTabBar_Explore_Link']", "底部导航探索"),
                ("//div[@data-testid='AppTabBar_Notifications_Link']", "底部导航通知")
            ]

            login_evidence_count = 0
            found_indicators = []

            for xpath, description in strict_login_indicators:
                elements = driver.find_elements(By.XPATH, xpath)
                if elements:
                    login_evidence_count += 1
                    found_indicators.append(description)
                    print(f"✓ 找到登录标志: {description}")

            # 5. 额外验证：尝试获取用户信息
            user_verification = False
            try:
                # 查找用户相关元素
                user_elements = driver.find_elements(By.XPATH, "//span[starts-with(text(), '@')]")
                if user_elements:
                    for element in user_elements:
                        text = element.text.strip()
                        if text.startswith('@') and len(text) > 3:  # @xxx格式且长度合理
                            print(f"✓ 找到用户标识: {text}")
                            user_verification = True
                            break
            except Exception as e:
                print(f"⚠️ 用户验证失败: {str(e)}")

            # 6. 综合判断（需要满足严格条件）
            print(f"📊 登录证据统计:")
            print(f"   - 登录标志数量: {login_evidence_count}")
            print(f"   - 用户验证: {'通过' if user_verification else '失败'}")
            print(f"   - 找到的标志: {', '.join(found_indicators)}")

            # 更严格的判断逻辑
            # 1. 必须有用户验证通过（找到@用户名）
            # 2. 必须有至少2个明确的登录标志
            # 3. 页面内容必须充足

            if not user_verification:
                print("❌ Twitter未登录（未找到用户标识）")
                if attempt < retry_count - 1:
                    print("🔄 未找到用户标识，刷新页面重试...")
                    driver.refresh()
                    time.sleep(5)
                    continue
                else:
                    return False

            if login_evidence_count < 2:
                print(f"❌ Twitter未登录（登录标志不足: {login_evidence_count}/2）")
                if attempt < retry_count - 1:
                    print("🔄 登录标志不足，刷新页面重试...")
                    driver.refresh()
                    time.sleep(5)
                    continue
                else:
                    return False

            # 通过所有验证
            print("✅ Twitter已登录（严格验证通过）")
            return True

        except Exception as e:
            print(f"❌ 检查Twitter登录状态时出错: {str(e)}")
            if attempt < retry_count - 1:
                print("🔄 出现异常，重试...")
                time.sleep(3)
                continue
            else:
                print("❌ 多次尝试失败，假设未登录")
                return False

    return False


def get_twitter_username(driver, retry_count=3):
    """
    获取当前登录的Twitter用户名

    Args:
        driver: WebDriver实例
        retry_count: 重试次数

    Returns:
        dict: 包含用户信息的字典 {'username': str, 'display_name': str, 'handle': str}
               如果获取失败返回None
    """
    for attempt in range(retry_count):
        try:
            print(f"👤 获取Twitter用户信息...（第{attempt + 1}/{retry_count}次尝试）")

            # 确保在Twitter主页
            current_url = driver.current_url.lower()
            if 'x.com' not in current_url and 'twitter.com' not in current_url:
                driver.get("https://x.com")
                time.sleep(3)

            user_info = {}

            # 方法1: 从侧边栏用户菜单获取
            try:
                # 查找用户菜单按钮
                user_menu_selectors = [
                    "//div[@data-testid='SideNav_AccountSwitcher_Button']",
                    "//div[@data-testid='UserAvatar-Container-unknown']",
                    "//div[contains(@class, 'css-1dbjc4n r-1awozwy')]//div[contains(@class, 'css-1dbjc4n r-1wbh5a2')]"
                ]

                for selector in user_menu_selectors:
                    menu_elements = driver.find_elements(By.XPATH, selector)
                    if menu_elements:
                        # 尝试从菜单中提取用户名
                        menu_text = menu_elements[0].text
                        if menu_text and '@' in menu_text:
                            lines = menu_text.split('\n')
                            for line in lines:
                                if line.startswith('@'):
                                    user_info['handle'] = line
                                    user_info['username'] = line[1:]  # 去掉@符号
                                    print(f"✅ 从用户菜单获取到用户名: {line}")
                                    break
                        break
            except Exception as e:
                print(f"⚠️ 从用户菜单获取用户名失败: {str(e)}")

            # 方法2: 从URL路径获取（如果在个人资料页面）
            if not user_info.get('username'):
                try:
                    current_url = driver.current_url
                    if '/profile' in current_url or current_url.count('/') >= 4:
                        # 尝试导航到个人资料页面
                        profile_links = driver.find_elements(By.XPATH, "//a[contains(@href, '/profile') or @aria-label='Profile']")
                        if profile_links:
                            profile_links[0].click()
                            time.sleep(2)

                            # 从URL获取用户名
                            new_url = driver.current_url
                            if 'x.com/' in new_url:
                                username_part = new_url.split('x.com/')[-1].split('/')[0]
                                if username_part and username_part not in ['home', 'explore', 'notifications', 'messages']:
                                    user_info['username'] = username_part
                                    user_info['handle'] = f"@{username_part}"
                                    print(f"✅ 从URL获取到用户名: @{username_part}")
                except Exception as e:
                    print(f"⚠️ 从URL获取用户名失败: {str(e)}")

            # 方法3: 从页面元素获取显示名称和用户名
            if not user_info.get('username'):
                try:
                    # 查找用户名相关元素
                    username_selectors = [
                        "//div[@data-testid='UserName']",
                        "//span[contains(text(), '@')]",
                        "//div[contains(@class, 'css-901oao')]//span[starts-with(text(), '@')]"
                    ]

                    for selector in username_selectors:
                        elements = driver.find_elements(By.XPATH, selector)
                        for element in elements:
                            text = element.text.strip()
                            if text.startswith('@') and len(text) > 1:
                                user_info['handle'] = text
                                user_info['username'] = text[1:]
                                print(f"✅ 从页面元素获取到用户名: {text}")
                                break
                        if user_info.get('username'):
                            break
                except Exception as e:
                    print(f"⚠️ 从页面元素获取用户名失败: {str(e)}")

            # 方法4: 通过点击用户头像获取
            if not user_info.get('username'):
                try:
                    # 查找并点击用户头像
                    avatar_selectors = [
                        "//div[@data-testid='SideNav_AccountSwitcher_Button']//img",
                        "//img[contains(@alt, 'avatar') or contains(@alt, 'profile')]",
                        "//div[contains(@class, 'css-1dbjc4n r-1awozwy')]//img"
                    ]

                    for selector in avatar_selectors:
                        avatars = driver.find_elements(By.XPATH, selector)
                        if avatars:
                            avatars[0].click()
                            time.sleep(2)

                            # 查找弹出的用户信息
                            popup_text = driver.find_elements(By.XPATH, "//div[@role='menu']//span[starts-with(text(), '@')]")
                            if popup_text:
                                handle = popup_text[0].text.strip()
                                user_info['handle'] = handle
                                user_info['username'] = handle[1:] if handle.startswith('@') else handle
                                print(f"✅ 从用户头像弹窗获取到用户名: {handle}")

                                # 点击其他地方关闭弹窗
                                driver.find_element(By.TAG_NAME, "body").click()
                                time.sleep(1)
                                break
                            break
                except Exception as e:
                    print(f"⚠️ 通过用户头像获取用户名失败: {str(e)}")

            # 方法5: 从页面源码中搜索
            if not user_info.get('username'):
                try:
                    page_source = driver.page_source
                    import re

                    # 搜索用户名模式
                    username_patterns = [
                        r'"screen_name":"([^"]+)"',
                        r'"username":"([^"]+)"',
                        r'@([a-zA-Z0-9_]+)',
                        r'twitter\.com/([a-zA-Z0-9_]+)',
                        r'x\.com/([a-zA-Z0-9_]+)'
                    ]

                    for pattern in username_patterns:
                        matches = re.findall(pattern, page_source)
                        for match in matches:
                            if match and len(match) > 2 and match not in ['home', 'explore', 'notifications', 'messages', 'search']:
                                user_info['username'] = match
                                user_info['handle'] = f"@{match}"
                                print(f"✅ 从页面源码获取到用户名: @{match}")
                                break
                        if user_info.get('username'):
                            break
                except Exception as e:
                    print(f"⚠️ 从页面源码获取用户名失败: {str(e)}")

            # 获取显示名称
            if user_info.get('username'):
                try:
                    display_name_selectors = [
                        f"//span[contains(text(), '{user_info['username']}')]/preceding-sibling::span",
                        "//div[@data-testid='UserName']//span[1]",
                        "//div[contains(@class, 'css-901oao')]//span[not(starts-with(text(), '@'))]"
                    ]

                    for selector in display_name_selectors:
                        elements = driver.find_elements(By.XPATH, selector)
                        for element in elements:
                            text = element.text.strip()
                            if text and not text.startswith('@') and len(text) > 0:
                                user_info['display_name'] = text
                                print(f"✅ 获取到显示名称: {text}")
                                break
                        if user_info.get('display_name'):
                            break
                except Exception as e:
                    print(f"⚠️ 获取显示名称失败: {str(e)}")

            # 如果成功获取到用户名，返回结果
            if user_info.get('username'):
                print(f"🎉 成功获取Twitter用户信息:")
                print(f"   用户名: @{user_info['username']}")
                if user_info.get('display_name'):
                    print(f"   显示名称: {user_info['display_name']}")
                return user_info

            # 如果这次尝试失败，重试
            if attempt < retry_count - 1:
                print("⚠️ 获取用户名失败，刷新页面重试...")
                driver.refresh()
                time.sleep(5)
                continue
            else:
                print("❌ 多次尝试后仍无法获取用户名")
                return None

        except Exception as e:
            print(f"❌ 获取Twitter用户名时出错: {str(e)}")
            if attempt < retry_count - 1:
                print("🔄 出现异常，重试...")
                time.sleep(3)
                continue
            else:
                return None

    return None


def log_user_info(wallet_address, user_info, new_token=None, log_file='twitter_users.log'):
    """
    记录Twitter用户信息到日志文件

    Args:
        wallet_address: 钱包地址
        user_info: 用户信息字典
        new_token: 新token（如果有）
        log_file: 日志文件路径
    """
    try:
        from datetime import datetime

        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        username = user_info.get('username', 'Unknown')
        display_name = user_info.get('display_name', '')
        handle = user_info.get('handle', f"@{username}")

        log_entry = f"[{timestamp}] 钱包: {wallet_address} | 用户名: {handle}"
        if display_name:
            log_entry += f" | 显示名: {display_name}"
        if new_token:
            log_entry += f" | 新Token: {new_token[:20]}..."
        log_entry += "\n"

        # 写入日志文件
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(log_entry)

        print(f"📝 用户信息已记录到 {log_file}")

    except Exception as e:
        print(f"⚠️ 记录用户信息失败: {str(e)}")


def log_failed_token(wallet_address, failed_token, log_file='failed_tokens.log'):
    """
    记录失败的token到日志文件

    Args:
        wallet_address: 钱包地址
        failed_token: 失败的token
        log_file: 日志文件路径
    """
    try:
        from datetime import datetime

        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_entry = f"[{timestamp}] 钱包: {wallet_address} | 失败Token: {failed_token[:20]}... | 完整Token: {failed_token}\n"

        # 写入日志文件
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(log_entry)

        print(f"📝 失败token已记录到 {log_file}")

    except Exception as e:
        print(f"⚠️ 记录失败token失败: {str(e)}")


def login_twitter_with_token(driver, token, retry_count=2):
    """
    使用token登录Twitter（增强版，处理网络问题）

    Args:
        driver: WebDriver实例
        token: Twitter auth_token
        retry_count: 重试次数

    Returns:
        bool: 登录是否成功
    """
    for attempt in range(retry_count):
        try:
            print(f"🔑 尝试使用token登录Twitter...（第{attempt + 1}/{retry_count}次尝试）")

            # 先访问Twitter获取域名cookie作用域
            driver.get("https://x.com")

            # 等待页面加载
            if not wait_for_page_load(driver, max_wait_time=20):
                if attempt < retry_count - 1:
                    print("⚠️ 页面加载失败，重试...")
                    continue
                else:
                    print("❌ 页面加载失败，无法设置token")
                    return False

            # 清除现有cookies
            print("🧹 清除现有cookies...")
            driver.delete_all_cookies()
            time.sleep(2)

            # 添加auth_token cookie
            try:
                driver.add_cookie({
                    'name': 'auth_token',
                    'value': token.replace('"', ''),
                    'domain': '.x.com',
                    'path': '/',
                    'secure': True
                })
                print("🍪 已设置auth_token cookie")
            except Exception as cookie_error:
                print(f"❌ 设置cookie失败: {str(cookie_error)}")
                if attempt < retry_count - 1:
                    continue
                else:
                    return False

            # 刷新页面完成登录
            print("🔄 刷新页面完成登录...")
            driver.refresh()

            # 等待登录完成
            if not wait_for_page_load(driver, max_wait_time=20):
                if attempt < retry_count - 1:
                    print("⚠️ 登录后页面加载失败，重试...")
                    continue
                else:
                    print("❌ 登录后页面加载失败")
                    return False

            # 检查是否登录成功
            if check_twitter_login_status(driver):
                print("✅ Twitter token登录成功")
                return True
            else:
                print("❌ Twitter token登录失败")
                if attempt < retry_count - 1:
                    print("🔄 登录验证失败，重试...")
                    time.sleep(3)
                    continue
                else:
                    return False

        except Exception as e:
            print(f"❌ Twitter token登录过程中出错: {str(e)}")
            if attempt < retry_count - 1:
                print("🔄 出现异常，重试...")
                time.sleep(5)
                continue
            else:
                return False

    return False


def process_profile(client, profile_data, available_tokens, accounts_data, profile_index):
    """
    处理单个配置文件

    Args:
        client: IXBrowserClient实例
        profile_data: 配置文件数据
        available_tokens: 可用的token列表
        accounts_data: 所有账户数据
        profile_index: 当前处理的账户索引

    Returns:
        tuple: (是否成功, 是否使用了新token)
    """
    wallet_address = profile_data['wallet_address']

    # 1. 查找配置文件
    profiles, error_code, error_message = get_profile_list(wallet_address)

    if not profiles:
        print(f"❌ 未找到配置文件: {wallet_address}")
        return False, False

    profile_id = profiles[0].get('profile_id')
    print(f"✅ 找到配置文件: {wallet_address} (ID: {profile_id})")

    # 2. 打开浏览器
    print(f"🚀 正在打开浏览器...")
    open_result = client.open_profile(profile_id, load_profile_info_page=False)

    if not open_result:
        print(f"❌ 打开浏览器失败: {client.code} - {client.message}")
        return False, False

    # 3. 连接Selenium
    debug_address = open_result.get('debugging_address', '')
    if debug_address.startswith('http://'):
        debug_address = debug_address[7:]
    elif debug_address.startswith('https://'):
        debug_address = debug_address[8:]

    chrome_options = Options()
    chrome_options.debugger_address = debug_address

    driver = None
    used_new_token = False

    try:
        # 初始化WebDriver
        webdriver_path = open_result.get('webdriver', '')
        if webdriver_path:
            service = webdriver.chrome.service.Service(executable_path=webdriver_path)
            driver = webdriver.Chrome(service=service, options=chrome_options)
        else:
            driver = webdriver.Chrome(options=chrome_options)

        print("✅ WebDriver连接成功")

        # 4. 打开Twitter（带重试机制）
        twitter_loaded = False
        for attempt in range(3):
            try:
                print(f"🐦 正在打开Twitter...（第{attempt + 1}/3次尝试）")
                driver.get("https://x.com")

                # 等待页面加载并检查是否成功
                if wait_for_page_load(driver):
                    twitter_loaded = True
                    break
                else:
                    if attempt < 2:
                        print("⚠️ Twitter页面加载失败，重试...")
                        time.sleep(5)
                    else:
                        print("❌ Twitter页面加载失败，跳过此账户")
                        return False, False

            except Exception as e:
                print(f"❌ 打开Twitter时出错: {str(e)}")
                if attempt < 2:
                    print("🔄 重试打开Twitter...")
                    time.sleep(5)
                else:
                    print("❌ 多次尝试失败，跳过此账户")
                    return False, False

        if not twitter_loaded:
            print("❌ 无法加载Twitter页面")
            return False, False

        # 5. 检查登录状态（使用增强版检测）
        is_logged_in = check_twitter_login_status(driver)

        if is_logged_in:
            print(f"✅ {wallet_address} Twitter已登录")

            # 获取当前登录的用户名
            user_info = get_twitter_username(driver)
            if user_info:
                current_username = user_info.get('username', 'Unknown')
                display_name = user_info.get('display_name', '')
                print(f"👤 当前登录用户: @{current_username}")
                if display_name:
                    print(f"📝 显示名称: {display_name}")

                # 可以选择将用户名信息保存到日志文件
                log_user_info(wallet_address, user_info)
            else:
                print("⚠️ 无法获取当前登录用户信息")

            print(f"⏭️ 跳过处理，继续下一个账户")
            return True, False

        # 6. 如果未登录，尝试使用新token登录（支持多token尝试）
        print(f"🔄 {wallet_address} Twitter未登录，尝试使用新token登录")

        if not available_tokens:
            print("❌ 没有可用的token")
            return False, False

        # 尝试多个token，直到成功或用完所有token
        max_token_attempts = min(3, len(available_tokens))  # 最多尝试3个token
        successful_token = None

        for attempt in range(max_token_attempts):
            if not available_tokens:
                print("❌ 没有更多可用的token")
                break

            # 取第一个可用token
            current_token = available_tokens[0]
            print(f"🎯 尝试第{attempt + 1}个token: {current_token[:20]}...")

            # 尝试登录
            login_success = login_twitter_with_token(driver, current_token)

            if login_success:
                print(f"✅ 第{attempt + 1}个token登录成功")
                successful_token = current_token

                # 获取新登录的用户信息
                user_info = get_twitter_username(driver)
                if user_info:
                    new_username = user_info.get('username', 'Unknown')
                    display_name = user_info.get('display_name', '')
                    print(f"👤 新登录用户: @{new_username}")
                    if display_name:
                        print(f"📝 显示名称: {display_name}")

                    # 记录用户信息
                    log_user_info(wallet_address, user_info, new_token=current_token)
                else:
                    print("⚠️ 无法获取新登录用户信息")

                # 更新账户数据中的token
                accounts_data[profile_index]['twitter_token'] = current_token

                # 实时更新accounts文件中的token
                update_account_token_in_file(wallet_address, current_token)

                # 从可用token列表中移除已使用的token
                available_tokens.remove(current_token)

                # 实时从token文件中移除已使用的token
                remove_used_token_from_file(current_token)

                return True, True

            else:
                print(f"❌ 第{attempt + 1}个token登录失败")

                # 将失败的token移到失败列表，从可用列表中移除
                failed_token = available_tokens.pop(0)

                # 记录失败的token到单独的文件
                log_failed_token(wallet_address, failed_token)

                # 实时从token文件中移除失败的token
                remove_used_token_from_file(failed_token)

                print(f"🗑️ 已移除失败的token: {failed_token[:20]}...")

                if attempt < max_token_attempts - 1 and available_tokens:
                    print(f"🔄 尝试下一个token...")
                    time.sleep(2)  # 短暂等待后尝试下一个token

        # 如果所有尝试都失败了
        print(f"❌ 尝试了{max_token_attempts}个token都失败，无法登录")
        return False, False

    except Exception as e:
        print(f"❌ 处理过程中出错: {str(e)}")
        return False, False
    finally:
        # 关闭浏览器
        if driver:
            try:
                print("🔒 正在关闭浏览器...")
                close_result = client.close_profile(profile_id)
                if close_result:
                    print("✅ 浏览器已关闭")
                else:
                    print(f"⚠️ API关闭失败，使用Selenium关闭")
                    driver.quit()
            except Exception as e:
                print(f"⚠️ 关闭浏览器时出错: {str(e)}")
                try:
                    driver.quit()
                except:
                    pass


def main():
    """主函数"""
    print("🔄 Twitter Token替换自动化脚本启动")
    print("=" * 50)

    # 配置文件路径
    accounts_file = 'accounts.txt'
    tokens_file = 'twitter.txt'

    # 初始化客户端
    client = IXBrowserClient()

    # 读取账户数据
    accounts_data = read_data_file(accounts_file)
    if not accounts_data:
        print("❌ 没有读取到有效的账户数据")
        return

    # 读取可用的Twitter tokens
    available_tokens = read_twitter_tokens(tokens_file)
    if not available_tokens:
        print("❌ 没有读取到可用的Twitter tokens")
        return

    print(f"📋 共找到 {len(accounts_data)} 个账户")
    print(f"🎯 共有 {len(available_tokens)} 个可用token")

    # 处理统计
    success_count = 0
    failed_count = 0
    token_replaced_count = 0

    # 处理每个账户
    for i, data in enumerate(accounts_data):
        wallet_address = data['wallet_address']
        print(f"\n{'='*20} 处理第 {i+1}/{len(accounts_data)} 个账户 {'='*20}")
        print(f"钱包地址: {wallet_address}")
        print(f"当前token: {data['twitter_token'][:20]}...")

        try:
            success, used_new_token = process_profile(
                client, data, available_tokens, accounts_data, i
            )

            if success:
                success_count += 1
                if used_new_token:
                    token_replaced_count += 1
                    print(f"✅ 账户 {wallet_address} 处理成功，已替换token")
                else:
                    print(f"✅ 账户 {wallet_address} 已登录，无需替换token")
            else:
                failed_count += 1
                print(f"❌ 账户 {wallet_address} 处理失败")

        except Exception as e:
            failed_count += 1
            print(f"❌ 处理账户 {wallet_address} 时发生异常: {str(e)}")

        # 如果没有可用token了，提前结束
        if not available_tokens:
            print("⚠️ 没有更多可用token，停止处理")
            break

        print(f"📊 剩余可用token: {len(available_tokens)} 个")

        # 在处理完当前账户后切换IP（如果不是最后一个账户）
        if i < len(accounts_data) - 1:  # 不是最后一个账户
            print(f"\n🌐 准备为下一个账户切换IP...")
            ip_switch_success = switch_ip()
            if not ip_switch_success:
                print("⚠️ IP切换失败，但继续处理下一个账户...")

    # 数据已实时保存，无需再次保存
    if token_replaced_count > 0:
        print(f"\n✅ 所有数据已实时保存到文件")

    # 输出统计结果
    print(f"\n{'='*50}")
    print(f"📊 处理完成统计:")
    print(f"   ✅ 成功: {success_count} 个")
    print(f"   ❌ 失败: {failed_count} 个")
    print(f"   🔄 替换token: {token_replaced_count} 个")
    print(f"   🎯 剩余可用token: {len(available_tokens)} 个")
    print("🎉 脚本执行完成！")


if __name__ == '__main__':
    main()
