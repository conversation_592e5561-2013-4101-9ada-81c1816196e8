import json
import os
import sys
import time
import shutil
import re

import httpx
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common import NoSuchElementException, TimeoutException, ElementClickInterceptedException

from ixbrowser_local_api import IXBrowserClient
from examples.auto_profile_setup import read_data_file, handle_failed_profile, safe_click

# 主要用来通过钱包授权批量登录 Zealy.io 账户

def switch_ip():
    """切换IP地址"""
    try:
        print("🔄 正在切换IP地址...")
        with httpx.Client(timeout=30.0) as http_client:
            response = http_client.get(
                "https://mproxy.vn/capi/_iCp27tGi46qKNBKo0TyUArkI-8rPGrkK7BB83SMAts/key/cWt6bAQdTeapT/resetIp"
            )
            if response.status_code == 200:
                print("✅ IP切换成功")
                time.sleep(10)  # 等待10秒让IP生效
                return True
            else:
                print(f"❌ IP切换失败，状态码: {response.status_code}")
                return False
    except Exception as e:
        print(f"❌ IP切换请求出错: {str(e)}")
        return False


def get_profile_list(name=None, timeout=20.0):
    """获取配置文件列表，包含备注信息"""
    client = None
    try:
        client = httpx.Client(timeout=timeout)
        url = "http://localhost:53200/api/v2/profile-list"
        params = {"page": 1, "limit": 100}

        if name:
            params["name"] = name

        response = client.post(url, json=params)
        data = response.json()

        if response.status_code == 200 and data.get("error", {}).get("code") == 0:
            profiles = data.get("data", {}).get("data", [])

            # 调试：显示返回的profile数据结构
            if profiles and len(profiles) > 0:
                first_profile = profiles[0]
                print(f"📋 Profile数据字段: {list(first_profile.keys())}")

                # 检查是否有note字段
                if 'note' in first_profile:
                    note_content = first_profile.get('note', '')
                    print(f"📝 找到note字段，内容长度: {len(note_content)} 字符")
                    if note_content:
                        print(f"📝 备注内容预览: {note_content[:100]}{'...' if len(note_content) > 100 else ''}")
                    else:
                        print(f"📝 note字段为空")
                else:
                    print(f"❌ 未找到note字段")

            return profiles, None, None

        error_code = data.get("error", {}).get("code")
        error_message = data.get("error", {}).get("message")
        return None, error_code, error_message

    except Exception as e:
        return None, None, str(e)
    finally:
        if client:
            client.close()


def find_profile_by_wallet_address(wallet_address):
    """根据钱包地址查找对应的浏览器配置文件（与twitter_email_extractor.py完全相同）"""
    try:
        print(f"🔍 查找钱包地址对应的浏览器配置: {wallet_address}")

        # 1. 查找配置文件（与twitter_email_extractor.py相同）
        profiles, error_code, error_message = get_profile_list(wallet_address)

        if not profiles:
            print(f"❌ 未找到配置文件: {wallet_address}")
            return None

        profile_id = profiles[0].get('profile_id')
        print(f"✅ 找到配置文件: {wallet_address} (ID: {profile_id})")

        return profile_id

    except Exception as e:
        print(f"❌ 查找配置文件时出错: {str(e)}")
        return None


def open_browser_by_profile_id(profile_id, client):
    """通过配置文件ID打开浏览器（与twitter_email_extractor.py完全相同）"""
    try:
        # 2. 打开浏览器
        print(f"🚀 正在打开浏览器...")
        try:
            open_result = client.open_profile(profile_id, load_profile_info_page=False)

            if not open_result:
                print(f"❌ 打开浏览器失败: {client.code} - {client.message}")
                print(f"⏭️ 跳过此浏览器，继续处理下一个")
                return None

            print(f"✅ 浏览器打开成功")

        except Exception as e:
            print(f"❌ 打开浏览器时出错: {str(e)}")
            print(f"⏭️ 跳过此浏览器，继续处理下一个")
            return None

        # 3. 获取调试地址并连接Selenium
        debug_address = open_result.get('debugging_address', '')
        if debug_address.startswith('http://'):
            debug_address = debug_address[7:]
        elif debug_address.startswith('https://'):
            debug_address = debug_address[8:]

        chrome_options = Options()
        chrome_options.debugger_address = debug_address

        driver = None
        try:
            # 初始化WebDriver
            webdriver_path = open_result.get('webdriver', '')
            if webdriver_path:
                service = webdriver.chrome.service.Service(executable_path=webdriver_path)
                driver = webdriver.Chrome(service=service, options=chrome_options)
            else:
                driver = webdriver.Chrome(options=chrome_options)

            # 设置更长的超时时间
            driver.set_page_load_timeout(180)  # 页面加载超时3分钟
            driver.implicitly_wait(30)  # 隐式等待30秒

            print("✅ WebDriver连接成功")
            return driver

        except Exception as e:
            print(f"❌ WebDriver连接失败: {str(e)}")
            return None

    except Exception as e:
        print(f"❌ 打开浏览器时出错: {str(e)}")
        return None


def close_browser_by_profile_id(profile_id, client):
    """通过配置文件ID关闭浏览器"""
    try:
        print(f"🔒 正在关闭浏览器配置文件: {profile_id}")

        result = client.close_profile(profile_id)

        if result:
            print(f"✅ 浏览器关闭成功")
            return True
        else:
            print(f"❌ 浏览器关闭失败: {client.code} - {client.message}")
            return False

    except Exception as e:
        print(f"❌ 关闭浏览器时出错: {str(e)}")
        return False


def check_zealy_login_status(driver):
    """检查 Zealy.io 登录状态"""
    try:
        print("🔍 检查 Zealy.io 登录状态...")

        # 等待页面加载
        time.sleep(3)

        # 检查登录状态的指标元素
        login_indicators = [
            # 用户头像或用户菜单
            "//img[contains(@class, 'avatar')]",
            "//div[contains(@class, 'avatar')]",
            "//button[contains(@class, 'avatar')]",
            # 用户名或用户信息
            "//div[contains(@class, 'user')]",
            "//span[contains(@class, 'username')]",
            "//div[contains(@class, 'profile')]",
            # 登录后的导航元素
            "//nav[contains(@class, 'user')]",
            "//div[contains(@class, 'user-menu')]",
            "//button[contains(@class, 'user-menu')]",
            # 钱包连接状态
            "//div[contains(@class, 'wallet-connected')]",
            "//button[contains(@class, 'wallet-connected')]",
            "//span[contains(text(), 'Connected')]",
            "//span[contains(text(), 'connected')]",
            # 断开连接按钮（说明已连接）
            "//button[contains(text(), 'Disconnect')]",
            "//button[contains(text(), 'disconnect')]",
            "//a[contains(text(), 'Disconnect')]",
            # 用户相关的下拉菜单
            "//div[@role='menu']",
            "//ul[contains(@class, 'dropdown')]",
            # 个人资料链接
            "//a[contains(@href, 'profile')]",
            "//a[contains(@href, 'account')]",
            "//a[contains(@href, 'settings')]"
        ]

        for indicator in login_indicators:
            try:
                elements = driver.find_elements(By.XPATH, indicator)
                for element in elements:
                    if element.is_displayed():
                        print(f"✅ 检测到登录指标: {indicator}")
                        return True
            except Exception as e:
                continue

        # 检查页面URL是否包含登录后的特征
        current_url = driver.current_url
        if any(keyword in current_url.lower() for keyword in ['dashboard', 'profile', 'account', 'user']):
            print(f"✅ URL显示已登录状态: {current_url}")
            return True

        print("❌ 未检测到登录状态")
        return False

    except Exception as e:
        print(f"❌ 检查登录状态时出错: {str(e)}")
        return False


def check_login_status_via_login_page(driver):
    """通过访问登录页面检测登录状态"""
    try:
        print("🔍 通过登录页面检测登录状态...")
        driver.get("https://zealy.io/login")

        # 等待页面加载
        time.sleep(5)

        # 如果已经登录，通常会被重定向到其他页面（如dashboard）
        current_url = driver.current_url.lower()

        # 检查是否被重定向到非登录页面
        if "/login" not in current_url:
            print(f"✅ 检测到已登录状态 - 被重定向到: {driver.current_url}")
            return True

        # 检查页面上是否有登录表单元素
        login_form_indicators = [
            "//input[@type='email']",
            "//input[@type='password']",
            "//button[contains(text(), 'Sign in')]",
            "//button[contains(text(), 'Login')]",
            "//button[contains(text(), 'Connect')]",
            "//div[contains(@class, 'login')]",
            "//form[contains(@class, 'login')]"
        ]

        has_login_form = False
        for indicator in login_form_indicators:
            try:
                elements = driver.find_elements(By.XPATH, indicator)
                if elements and any(elem.is_displayed() for elem in elements):
                    has_login_form = True
                    break
            except:
                continue

        if has_login_form:
            print("❌ 检测到登录表单，账户未登录")
            return False
        else:
            print("✅ 未找到登录表单，可能已登录")
            return True

    except Exception as e:
        print(f"❌ 检测登录状态时出错: {str(e)}")
        return False


def navigate_to_zealy(driver):
    """导航到 Zealy.io 网站"""
    try:
        print("🌐 正在检测 Zealy.io 登录状态...")

        # 首先检查登录状态
        if check_login_status_via_login_page(driver):
            print("✅ 检测到已登录状态，跳过登录流程")
            # 导航到目标页面
            print("🌐 导航到目标页面...")
            driver.get("https://zealy.io/cw/coingarage/questboard/")
            time.sleep(5)
            return "already_logged_in"

        print("❌ 检测到未登录状态，需要进行登录")
        print("🌐 正在访问 Zealy.io 主页...")
        driver.get("https://zealy.io/cw/coingarage/questboard/")

        # 等待页面加载
        time.sleep(5)

        # 检查页面是否正确加载
        if "zealy" in driver.current_url.lower():
            print("✅ 成功访问 Zealy.io，需要进行登录")
            return True
        else:
            print(f"❌ 页面加载异常，当前URL: {driver.current_url}")
            return False

    except Exception as e:
        print(f"❌ 访问 Zealy.io 时出错: {str(e)}")
        return False


def click_login_with_wallet(driver):
    """点击 'Log in with wallet' 按钮"""
    try:
        print("🔍 查找并点击 'Log in with wallet' 按钮...")

        # 可能的 "Log in with wallet" 按钮选择器
        wallet_login_selectors = [
            "//button[contains(text(), 'Log in with wallet')]",
            "//button[contains(text(), 'log in with wallet')]",
            "//button[contains(text(), 'Login with wallet')]",
            "//button[contains(text(), 'login with wallet')]",
            "//button[contains(text(), 'Connect wallet')]",
            "//button[contains(text(), 'connect wallet')]",
            "//button[contains(text(), 'Wallet')]",
            "//button[contains(text(), 'wallet')]",
            "//a[contains(text(), 'Log in with wallet')]",
            "//a[contains(text(), 'log in with wallet')]",
            "//a[contains(text(), 'Login with wallet')]",
            "//a[contains(text(), 'login with wallet')]",
            "//a[contains(text(), 'Connect wallet')]",
            "//a[contains(text(), 'connect wallet')]",
            "//div[contains(text(), 'Log in with wallet')]",
            "//div[contains(text(), 'log in with wallet')]",
            "//div[contains(text(), 'Login with wallet')]",
            "//div[contains(text(), 'login with wallet')]",
            "//span[contains(text(), 'Log in with wallet')]",
            "//span[contains(text(), 'log in with wallet')]"
        ]

        for selector in wallet_login_selectors:
            try:
                elements = driver.find_elements(By.XPATH, selector)

                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        element.click()
                        print(f"✅ 成功点击 'Log in with wallet' 按钮")
                        time.sleep(3)
                        return True

            except Exception as e:
                continue

        print("❌ 未找到 'Log in with wallet' 按钮")
        return False

    except Exception as e:
        print(f"❌ 点击 'Log in with wallet' 按钮时出错: {str(e)}")
        return False


def analyze_wallet_options(driver):
    """分析页面上的所有钱包选项"""
    try:
        print("🔍 分析页面上的所有钱包选项...")

        # 等待钱包选择弹窗完全加载
        time.sleep(8)

        # 查找所有可能的钱包元素
        all_elements = []

        # 查找所有按钮
        buttons = driver.find_elements(By.TAG_NAME, "button")
        print(f"📋 找到 {len(buttons)} 个按钮:")
        for i, btn in enumerate(buttons):
            try:
                if btn.is_displayed():
                    text = btn.text.strip()
                    classes = btn.get_attribute('class')
                    if text:  # 只显示有文本的按钮
                        print(f"  {i+1}. 按钮文本: '{text}' | 类名: {classes[:100]}...")
                        all_elements.append({
                            'type': 'button',
                            'text': text,
                            'element': btn,
                            'classes': classes
                        })
            except:
                continue

        # 查找所有div
        divs = driver.find_elements(By.TAG_NAME, "div")
        wallet_divs = []
        for div in divs:
            try:
                if div.is_displayed():
                    text = div.text.strip()
                    classes = div.get_attribute('class')
                    # 查找可能包含钱包名称的div
                    if text and any(keyword in text.lower() for keyword in ['wallet', 'okx', 'metamask', 'coinbase', 'rainbow', 'connect']):
                        wallet_divs.append({
                            'type': 'div',
                            'text': text,
                            'element': div,
                            'classes': classes
                        })
            except:
                continue

        print(f"📋 找到 {len(wallet_divs)} 个钱包相关的div:")
        for i, div_info in enumerate(wallet_divs[:10]):  # 只显示前10个
            text = div_info['text'][:100]
            classes = div_info['classes'][:100] if div_info['classes'] else 'N/A'
            print(f"  {i+1}. Div文本: '{text}' | 类名: {classes}...")
            all_elements.append(div_info)

        # 查找所有图片（钱包图标）
        images = driver.find_elements(By.TAG_NAME, "img")
        wallet_images = []
        for img in images:
            try:
                if img.is_displayed():
                    alt = img.get_attribute('alt') or ''
                    src = img.get_attribute('src') or ''
                    if any(keyword in (alt + src).lower() for keyword in ['okx', 'wallet', 'metamask', 'coinbase']):
                        wallet_images.append({
                            'type': 'img',
                            'alt': alt,
                            'src': src,
                            'element': img.find_element(By.XPATH, '..')  # 获取父元素
                        })
            except:
                continue

        print(f"📋 找到 {len(wallet_images)} 个钱包相关的图片:")
        for i, img_info in enumerate(wallet_images):
            print(f"  {i+1}. 图片alt: '{img_info['alt']}' | src: {img_info['src'][:50]}...")
            all_elements.append(img_info)

        return all_elements

    except Exception as e:
        print(f"❌ 分析钱包选项时出错: {str(e)}")
        return []


def find_okx_wallet_element(all_elements):
    """从所有元素中查找 OKX 钱包元素"""
    try:
        print("🔍 在所有元素中查找 OKX 钱包...")

        okx_keywords = ['okx', 'OKX', 'Okx']

        for element_info in all_elements:
            element_type = element_info['type']

            if element_type == 'button':
                text = element_info['text'].lower()
                classes = element_info.get('classes', '').lower()

                # 检查按钮文本或类名中是否包含 OKX
                if any(keyword.lower() in text for keyword in okx_keywords) or \
                   any(keyword.lower() in classes for keyword in okx_keywords):
                    print(f"✅ 找到 OKX 按钮: '{element_info['text']}'")
                    return element_info['element']

            elif element_type == 'div':
                text = element_info['text'].lower()
                classes = element_info.get('classes', '').lower()

                # 检查div文本或类名中是否包含 OKX
                if any(keyword.lower() in text for keyword in okx_keywords) or \
                   any(keyword.lower() in classes for keyword in okx_keywords):
                    print(f"✅ 找到 OKX div: '{element_info['text'][:50]}...'")
                    return element_info['element']

            elif element_type == 'img':
                alt = element_info.get('alt', '').lower()
                src = element_info.get('src', '').lower()

                # 检查图片alt或src中是否包含 OKX
                if any(keyword.lower() in alt for keyword in okx_keywords) or \
                   any(keyword.lower() in src for keyword in okx_keywords):
                    print(f"✅ 找到 OKX 图片: alt='{element_info['alt']}' src='{element_info['src'][:50]}...'")
                    return element_info['element']

        print("❌ 未在任何元素中找到 OKX 钱包")
        return None

    except Exception as e:
        print(f"❌ 查找 OKX 钱包元素时出错: {str(e)}")
        return None


def select_okx_wallet(driver, max_retries=3):
    """选择 OKX 钱包，如果没有找到则刷新页面重试"""
    try:
        for attempt in range(max_retries):
            print(f"🔍 第 {attempt + 1} 次尝试选择 OKX 钱包...")

            # 分析页面上的所有钱包选项
            all_elements = analyze_wallet_options(driver)

            if not all_elements:
                print("❌ 未找到任何钱包选项")
                continue

            # 查找 OKX 钱包元素
            okx_element = find_okx_wallet_element(all_elements)

            if okx_element:
                try:
                    # 滚动到元素可见
                    driver.execute_script("arguments[0].scrollIntoView(true);", okx_element)
                    time.sleep(1)

                    # 点击 OKX 钱包
                    okx_element.click()
                    print(f"✅ 成功选择 OKX 钱包")
                    time.sleep(3)
                    return True

                except Exception as e:
                    print(f"❌ 点击 OKX 钱包时出错: {str(e)}")
                    continue
            else:
                print(f"❌ 第 {attempt + 1} 次未找到 OKX 钱包选项")

                if attempt < max_retries - 1:
                    print("🔄 刷新页面重新尝试...")
                    driver.refresh()
                    time.sleep(5)

                    # 重新点击 "Log in with wallet" 按钮
                    if not click_login_with_wallet(driver):
                        print("❌ 刷新后无法找到 'Log in with wallet' 按钮")
                        continue
                else:
                    print("❌ 达到最大重试次数，未找到 OKX 钱包选项")
                    return False

        return False

    except Exception as e:
        print(f"❌ 选择 OKX 钱包时出错: {str(e)}")
        return False


def input_okx_password(driver, password="Aa2006123!!"):
    """在 OKX 钱包窗口中输入密码"""
    try:
        print(f"🔑 尝试输入 OKX 钱包密码...")

        # 等待页面加载
        time.sleep(3)

        # 可能的密码输入框选择器
        password_selectors = [
            "//input[@type='password']",
            "//input[contains(@placeholder, 'password')]",
            "//input[contains(@placeholder, 'Password')]",
            "//input[contains(@placeholder, '密码')]",
            "//input[contains(@class, 'password')]",
            "//input[contains(@name, 'password')]",
            "//input[contains(@id, 'password')]",
            "//input[@autocomplete='current-password']",
            "//input[@autocomplete='password']"
        ]

        for selector in password_selectors:
            try:
                elements = driver.find_elements(By.XPATH, selector)

                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        print(f"✅ 找到密码输入框")

                        # 清空输入框并输入密码
                        element.clear()
                        element.send_keys(password)
                        print(f"✅ 成功输入密码")

                        # 等待一下让密码输入生效
                        time.sleep(2)

                        # 尝试按回车键或查找确认按钮
                        try:
                            from selenium.webdriver.common.keys import Keys
                            element.send_keys(Keys.RETURN)
                            print("✅ 按回车键确认密码")
                            time.sleep(2)
                        except:
                            pass

                        return True

            except Exception as e:
                continue

        print("❌ 未找到密码输入框")
        return False

    except Exception as e:
        print(f"❌ 输入密码时出错: {str(e)}")
        return False


def find_okx_wallet_window(driver, main_window):
    """查找 OKX 钱包窗口"""
    try:
        max_wait_time = 30
        start_time = time.time()

        while time.time() - start_time < max_wait_time:
            all_windows = driver.window_handles
            print(f"📋 当前窗口数量: {len(all_windows)}")

            if len(all_windows) > 1:
                # 找到 OKX 钱包窗口
                for window_handle in all_windows:
                    if window_handle != main_window:
                        try:
                            driver.switch_to.window(window_handle)
                            window_title = driver.title
                            current_url = driver.current_url

                            print(f"🔍 检查窗口: {window_title} - {current_url}")

                            # 检查是否是 OKX 钱包窗口
                            if ("okx" in window_title.lower() or
                                "okx" in current_url.lower() or
                                "wallet" in window_title.lower() or
                                "extension" in current_url.lower()):

                                print(f"✅ 找到 OKX 钱包窗口: {window_title}")
                                return window_handle

                        except Exception as e:
                            print(f"⚠️ 检查窗口时出错: {str(e)}")
                            continue

            time.sleep(2)

        return None

    except Exception as e:
        print(f"❌ 查找 OKX 钱包窗口时出错: {str(e)}")
        return None


def check_need_additional_authorization(driver):
    """检查是否需要额外的授权"""
    try:
        print("🔍 检查是否需要额外授权...")

        # 等待页面加载
        time.sleep(2)

        # 查找可能的额外授权按钮
        additional_auth_selectors = [
            "//button[contains(text(), 'Authorize')]",
            "//button[contains(text(), 'Connect')]",
            "//button[contains(text(), 'Confirm')]",
            "//button[contains(text(), 'Allow')]",
            "//button[contains(text(), 'Approve')]",
            "//button[contains(text(), '授权')]",
            "//button[contains(text(), '连接')]",
            "//button[contains(text(), '确认')]",
            "//button[contains(text(), '同意')]"
        ]

        for selector in additional_auth_selectors:
            try:
                elements = driver.find_elements(By.XPATH, selector)
                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        print(f"✅ 发现需要额外授权的按钮: {element.text}")
                        return True
            except:
                continue

        print("❌ 未发现需要额外授权的按钮")
        return False

    except Exception as e:
        print(f"❌ 检查额外授权时出错: {str(e)}")
        return False


def check_authorization_completed(driver):
    """检查授权是否完成"""
    try:
        print("🔍 检查授权是否完成...")

        # 等待页面响应
        time.sleep(3)

        # 检查是否还有授权按钮（如果没有，说明授权完成）
        auth_button_selectors = [
            "//button[contains(text(), 'Authorize')]",
            "//button[contains(text(), 'Connect')]",
            "//button[contains(text(), 'Confirm')]",
            "//button[contains(text(), 'Allow')]",
            "//button[contains(text(), 'Approve')]"
        ]

        has_auth_buttons = False
        for selector in auth_button_selectors:
            try:
                elements = driver.find_elements(By.XPATH, selector)
                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        has_auth_buttons = True
                        break
                if has_auth_buttons:
                    break
            except:
                continue

        if not has_auth_buttons:
            print("✅ 未发现授权按钮，授权可能已完成")
            return True
        else:
            print("⚠️ 仍有授权按钮，授权可能未完成")
            return False

    except Exception as e:
        print(f"❌ 检查授权完成状态时出错: {str(e)}")
        return False


def open_wallet_plugin(driver):
    """打开钱包插件"""
    try:
        print("🔍 尝试打开钱包插件...")

        # 尝试通过扩展程序URL打开OKX钱包
        okx_extension_urls = [
            "chrome-extension://mcohilncbfahbmgdjkbpemcciiolgcge/popup.html",  # OKX常见扩展ID
            "chrome-extension://dkdedlpgdmmkkfjabffeganieamfklkm/popup.html",  # 另一个可能的OKX扩展ID
        ]

        for url in okx_extension_urls:
            try:
                print(f"🔍 尝试打开扩展URL: {url}")
                driver.execute_script(f"window.open('{url}', '_blank');")
                time.sleep(3)

                # 检查是否成功打开了新窗口
                all_windows = driver.window_handles
                if len(all_windows) > 1:
                    # 切换到新窗口
                    for window_handle in all_windows:
                        if window_handle != driver.current_window_handle:
                            driver.switch_to.window(window_handle)
                            current_url = driver.current_url
                            if "chrome-extension" in current_url and "okx" in current_url.lower():
                                print(f"✅ 成功打开OKX钱包插件: {current_url}")
                                return True
                            driver.close()  # 关闭不是OKX的窗口
                            driver.switch_to.window(driver.window_handles[0])
            except Exception as e:
                print(f"⚠️ 打开扩展URL失败: {url} - {str(e)}")
                continue

        # 如果直接URL打开失败，尝试通过页面元素打开
        print("🔍 尝试通过页面元素打开钱包插件...")

        # 查找可能的钱包图标或按钮
        wallet_selectors = [
            "//img[contains(@src, 'okx')]",
            "//img[contains(@alt, 'okx')]",
            "//button[contains(@class, 'wallet')]",
            "//div[contains(@class, 'wallet')]",
            "//span[contains(text(), 'OKX')]",
            "//div[contains(text(), 'OKX')]"
        ]

        for selector in wallet_selectors:
            try:
                elements = driver.find_elements(By.XPATH, selector)
                for element in elements:
                    if element.is_displayed():
                        element.click()
                        print(f"✅ 点击钱包元素: {selector}")
                        time.sleep(3)

                        # 检查是否打开了新窗口
                        all_windows = driver.window_handles
                        if len(all_windows) > 1:
                            return True
            except Exception as e:
                continue

        print("❌ 无法打开钱包插件")
        return False

    except Exception as e:
        print(f"❌ 打开钱包插件时出错: {str(e)}")
        return False


def check_wallet_plugin_authorization(driver):
    """检查钱包插件是否还需要授权"""
    try:
        print("🔍 检查钱包插件授权状态...")

        # 记录当前窗口
        main_window = driver.current_window_handle

        # 打开钱包插件
        if not open_wallet_plugin(driver):
            print("❌ 无法打开钱包插件")
            return False

        # 查找钱包插件窗口
        all_windows = driver.window_handles
        plugin_window = None

        for window_handle in all_windows:
            if window_handle != main_window:
                try:
                    driver.switch_to.window(window_handle)
                    current_url = driver.current_url
                    window_title = driver.title

                    print(f"🔍 检查窗口: {window_title} - {current_url}")

                    # 检查是否是钱包插件窗口
                    if ("chrome-extension" in current_url and
                        ("okx" in current_url.lower() or "okx" in window_title.lower())):
                        plugin_window = window_handle
                        print(f"✅ 找到钱包插件窗口")
                        break
                except Exception as e:
                    continue

        if not plugin_window:
            print("❌ 未找到钱包插件窗口")
            driver.switch_to.window(main_window)
            return False

        # 在钱包插件窗口中检查是否还有授权按钮
        driver.switch_to.window(plugin_window)

        # 等待页面加载
        time.sleep(3)

        # 查找授权按钮
        auth_button_selectors = [
            "//button[contains(text(), 'Authorize')]",
            "//button[contains(text(), 'Connect')]",
            "//button[contains(text(), 'Confirm')]",
            "//button[contains(text(), 'Allow')]",
            "//button[contains(text(), 'Approve')]",
            "//button[contains(text(), '授权')]",
            "//button[contains(text(), '连接')]",
            "//button[contains(text(), '确认')]",
            "//button[contains(text(), '同意')]"
        ]

        has_auth_button = False
        for selector in auth_button_selectors:
            try:
                elements = driver.find_elements(By.XPATH, selector)
                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        print(f"🔍 发现授权按钮: {element.text}")
                        # 点击授权按钮
                        element.click()
                        print(f"✅ 点击授权按钮成功")
                        has_auth_button = True
                        time.sleep(3)
                        break
                if has_auth_button:
                    break
            except Exception as e:
                continue

        # 关闭插件窗口
        try:
            driver.close()
        except:
            pass

        # 切换回主窗口
        driver.switch_to.window(main_window)

        if has_auth_button:
            print("✅ 在钱包插件中完成了额外授权")
            return True
        else:
            print("✅ 钱包插件中无需额外授权，登录完成")
            return True

    except Exception as e:
        print(f"❌ 检查钱包插件授权时出错: {str(e)}")
        # 确保切换回主窗口
        try:
            driver.switch_to.window(main_window)
        except:
            pass
        return False


def handle_okx_authorization(driver, password="Aa2006123!!", max_attempts=3):
    """处理 OKX 钱包授权，点击授权后刷新页面并检测插件"""
    try:
        print("🔐 处理 OKX 钱包授权...")

        # 记录当前窗口句柄
        main_window = driver.current_window_handle
        print(f"📋 主窗口句柄: {main_window}")

        for attempt in range(max_attempts):
            print(f"🔄 第 {attempt + 1} 次授权尝试...")

            # 等待 OKX 钱包弹窗出现
            time.sleep(5)

            # 查找 OKX 钱包窗口
            okx_window = find_okx_wallet_window(driver, main_window)

            if okx_window:
                print(f"✅ 找到 OKX 钱包窗口")

                # 切换到 OKX 钱包窗口
                driver.switch_to.window(okx_window)

                # 先尝试输入密码
                if input_okx_password(driver, password):
                    print("✅ OKX 密码输入成功")
                else:
                    print("⚠️ 未找到密码输入框或密码已输入")

                # 点击授权按钮
                if click_authorize_button(driver):
                    print("✅ 授权按钮点击成功")

                    # 切换回主窗口
                    driver.switch_to.window(main_window)

                    # 刷新页面
                    print("🔄 刷新页面...")
                    driver.refresh()
                    time.sleep(5)

                    # 打开钱包插件并检测是否还需要授权
                    if check_wallet_plugin_authorization(driver):
                        print("✅ OKX 钱包授权完成")
                        return True
                    else:
                        print("⚠️ 钱包插件仍需要授权，继续下一次尝试")
                else:
                    print("❌ 授权按钮点击失败")
                    # 切换回主窗口
                    driver.switch_to.window(main_window)
            else:
                print("❌ 未找到 OKX 钱包窗口")

            # 如果不是最后一次尝试，等待一下再重试
            if attempt < max_attempts - 1:
                print("⏳ 等待5秒后重试...")
                time.sleep(5)

        print("❌ 所有授权尝试都失败了")
        return False

    except Exception as e:
        print(f"❌ 处理 OKX 钱包授权时出错: {str(e)}")
        return False


def click_authorize_button(driver):
    """在 OKX 钱包窗口中点击授权按钮"""
    try:
        print("🔍 查找并点击授权按钮...")
        
        # 等待页面加载
        time.sleep(3)
        
        # 可能的授权按钮选择器
        authorize_selectors = [
            "//button[contains(text(), 'Authorize')]",
            "//button[contains(text(), 'Connect')]",
            "//button[contains(text(), 'Confirm')]",
            "//button[contains(text(), 'Allow')]",
            "//button[contains(text(), 'Approve')]",
            "//button[contains(text(), '授权')]",
            "//button[contains(text(), '连接')]",
            "//button[contains(text(), '确认')]",
            "//button[contains(text(), '同意')]",
            "//span[contains(text(), 'Authorize')]/..",
            "//span[contains(text(), 'Connect')]/..",
            "//span[contains(text(), 'Confirm')]/..",
            "//span[contains(text(), 'Allow')]/..",
            "//span[contains(text(), 'Approve')]/..",
            "//div[contains(@class, 'authorize')]",
            "//div[contains(@class, 'connect')]",
            "//div[contains(@class, 'confirm')]"
        ]
        
        for selector in authorize_selectors:
            try:
                print(f"🔍 尝试授权按钮选择器: {selector}")
                elements = driver.find_elements(By.XPATH, selector)
                
                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        element.click()
                        print(f"✅ 成功点击授权按钮")
                        time.sleep(3)
                        return True
                        
            except Exception as e:
                print(f"⚠️ 选择器失败: {selector} - {str(e)}")
                continue
        
        print("❌ 未找到可用的授权按钮")
        return False
        
    except Exception as e:
        print(f"❌ 点击授权按钮时出错: {str(e)}")
        return False


def process_zealy_login(profile_data, client):
    """处理单个账户的 Zealy 登录流程"""
    wallet_address = profile_data['wallet_address']
    profile_id = None
    driver = None

    try:
        print(f"\n{'='*60}")
        print(f"🚀 开始处理钱包地址: {wallet_address}")
        print(f"{'='*60}")

        # 1. 切换IP地址
        print("🌐 准备切换IP地址...")
        ip_switch_success = switch_ip()
        if not ip_switch_success:
            print("⚠️ IP切换失败，但继续处理...")

        # 2. 查找对应的浏览器配置文件
        profile_id = find_profile_by_wallet_address(wallet_address)
        if not profile_id:
            print(f"❌ 未找到钱包地址 {wallet_address} 对应的浏览器配置文件，跳过")
            return False

        # 3. 打开浏览器
        driver = open_browser_by_profile_id(profile_id, client)
        if not driver:
            print(f"❌ 浏览器打开失败，跳过该账户")
            return False

        # 4. 等待浏览器完全启动
        print("⏳ 等待浏览器完全启动...")
        time.sleep(5)

        # 5. 访问登录页面检测登录状态
        print("🌐 正在访问 Zealy.io 登录页面...")
        try:
            # 设置页面加载超时
            driver.set_page_load_timeout(60)

            print("📍 开始访问: https://zealy.io/login")
            driver.get("https://zealy.io/login")

            # 等待页面完全加载
            print("⏳ 等待页面完全加载...")
            time.sleep(10)

            print(f"📍 访问后的URL: {driver.current_url}")
            print(f"📄 页面标题: {driver.title}")

            # 检查页面是否正确加载
            if not driver.current_url:
                print("❌ 页面URL为空，可能加载失败")
                return False

            if "zealy" not in driver.current_url.lower():
                print(f"❌ 页面未正确加载到Zealy网站，当前URL: {driver.current_url}")
                return False

            # 检测登录状态
            current_url = driver.current_url.lower()

            print(f"🔍 检查URL是否包含'/login': {'/login' in current_url}")

            if "/login" not in current_url:
                print(f"✅ 检测到已登录状态 - 被重定向到: {driver.current_url}")
                print(f"✅ 钱包地址 {wallet_address} 已经登录 Zealy.io，跳过登录流程")
                return True

            print("❌ 检测到未登录状态，页面停留在登录页面")

        except Exception as e:
            print(f"❌ 访问登录页面时出错: {str(e)}")
            print(f"📍 当前URL: {driver.current_url}")
            return False

        # 6. 导航到目标页面进行登录
        print("🌐 正在访问 Zealy.io 主页...")
        driver.get("https://zealy.io/cw/coingarage/questboard/")
        time.sleep(5)

        # 7. 点击 "Log in with wallet" 按钮（只有在未登录时才执行）
        if not click_login_with_wallet(driver):
            print(f"❌ 点击 'Log in with wallet' 按钮失败")
            return False

        # 8. 选择 OKX 钱包
        if not select_okx_wallet(driver):
            print(f"❌ 选择 OKX 钱包失败")
            return False

        # 9. 处理 OKX 钱包授权
        if not handle_okx_authorization(driver):
            print(f"❌ OKX 钱包授权失败")
            return False

        print(f"✅ 钱包地址 {wallet_address} 登录 Zealy.io 成功！")
        
        # 等待一段时间确保登录完成
        time.sleep(5)
        
        return True
        
    except Exception as e:
        print(f"❌ 处理账户 {wallet_address} 时出错: {str(e)}")
        return False
        
    finally:
        # 清理资源
        try:
            if driver:
                driver.quit()
                print("🔒 浏览器已关闭")
        except:
            pass
            
        try:
            if profile_id:
                close_browser_by_profile_id(profile_id, client)
        except:
            pass


def main():
    """主函数"""
    try:
        print("🚀 开始 Zealy.io 钱包授权登录脚本")
        print("=" * 60)
        
        # 读取账户数据文件
        data_file = input("请输入数据文件路径 (默认: accounts.txt): ").strip()
        if not data_file:
            data_file = "accounts.txt"
        
        if not os.path.exists(data_file):
            print(f"❌ 数据文件 {data_file} 不存在")
            return
        
        # 初始化客户端（与twitter_email_extractor.py相同）
        client = IXBrowserClient()

        # 读取账户数据
        profiles_data = read_data_file(data_file)
        print(f"📋 读取到 {len(profiles_data)} 个账户")

        if not profiles_data:
            print("❌ 没有读取到有效的账户数据")
            return

        # 处理每个账户
        success_count = 0
        failed_count = 0

        for i, profile_data in enumerate(profiles_data, 1):
            try:
                print(f"\n🔄 处理第 {i}/{len(profiles_data)} 个账户")

                # 处理登录（传递client参数）
                if process_zealy_login(profile_data, client):
                    success_count += 1
                    print(f"✅ 第 {i} 个账户处理成功")
                else:
                    failed_count += 1
                    print(f"❌ 第 {i} 个账户处理失败")
                    # 将失败的账户保存到文件
                    handle_failed_profile(profile_data, 'failed_zealy_login.txt')

                # 账户间等待30秒
                if i < len(profiles_data):
                    print("⏳ 等待30秒后处理下一个账户...")
                    time.sleep(30)
                
            except Exception as e:
                failed_count += 1
                print(f"❌ 处理第 {i} 个账户时出错: {str(e)}")
                handle_failed_profile(profile_data, 'failed_zealy_login.txt')
                continue
        
        # 输出统计结果
        print(f"\n{'='*60}")
        print(f"📊 处理完成统计:")
        print(f"✅ 成功: {success_count} 个账户")
        print(f"❌ 失败: {failed_count} 个账户")
        print(f"📋 总计: {len(profiles_data)} 个账户")
        print(f"{'='*60}")
        
        if failed_count > 0:
            print(f"💾 失败的账户已保存到 failed_zealy_login.txt")
        
    except Exception as e:
        print(f"❌ 主程序执行时出错: {str(e)}")
        import traceback
        print(f"🔍 详细错误信息: {traceback.format_exc()}")


if __name__ == "__main__":
    main()
