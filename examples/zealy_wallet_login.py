import json
import os
import sys
import time
import shutil
import re

import httpx
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common import NoSuchElementException, TimeoutException, ElementClickInterceptedException

from ixbrowser_local_api import IXBrowserClient
from examples.auto_profile_setup import read_data_file, handle_failed_profile, safe_click

# 主要用来通过钱包授权批量登录 Zealy.io 账户

def switch_ip():
    """切换IP地址"""
    try:
        print("🔄 正在切换IP地址...")
        with httpx.Client(timeout=30.0) as http_client:
            response = http_client.get(
                "https://mproxy.vn/capi/_iCp27tGi46qKNBKo0TyUArkI-8rPGrkK7BB83SMAts/key/cWt6bAQdTeapT/resetIp"
            )
            if response.status_code == 200:
                print("✅ IP切换成功")
                time.sleep(10)  # 等待10秒让IP生效
                return True
            else:
                print(f"❌ IP切换失败，状态码: {response.status_code}")
                return False
    except Exception as e:
        print(f"❌ IP切换请求出错: {str(e)}")
        return False


def get_profile_list(name=None, timeout=20.0):
    """获取配置文件列表，包含备注信息"""
    client = None
    try:
        client = httpx.Client(timeout=timeout)
        url = "http://localhost:53200/api/v2/profile-list"
        params = {"page": 1, "limit": 100}

        if name:
            params["name"] = name

        response = client.post(url, json=params)
        data = response.json()

        if response.status_code == 200 and data.get("error", {}).get("code") == 0:
            profiles = data.get("data", {}).get("data", [])
            return profiles, None, None

        error_code = data.get("error", {}).get("code")
        error_message = data.get("error", {}).get("message")
        return None, error_code, error_message

    except Exception as e:
        return None, None, str(e)
    finally:
        if client:
            client.close()


def find_profile_by_wallet_address(wallet_address):
    """根据钱包地址查找对应的浏览器配置文件"""
    try:
        print(f"🔍 查找钱包地址对应的浏览器配置: {wallet_address}")
        
        profiles, error_code, error_message = get_profile_list()
        
        if profiles is None:
            print(f"❌ 获取配置文件列表失败: {error_message}")
            return None
            
        # 在配置文件的备注中查找钱包地址
        for profile in profiles:
            profile_id = profile.get('id')
            profile_name = profile.get('name', '')
            note = profile.get('note', '')
            
            # 检查备注中是否包含钱包地址
            if wallet_address.lower() in note.lower():
                print(f"✅ 找到匹配的配置文件: {profile_name} (ID: {profile_id})")
                return profile_id
                
            # 也检查配置文件名称中是否包含钱包地址
            if wallet_address.lower() in profile_name.lower():
                print(f"✅ 在配置文件名称中找到匹配: {profile_name} (ID: {profile_id})")
                return profile_id
        
        print(f"❌ 未找到钱包地址 {wallet_address} 对应的配置文件")
        return None
        
    except Exception as e:
        print(f"❌ 查找配置文件时出错: {str(e)}")
        return None


def open_browser_by_profile_id(profile_id):
    """通过配置文件ID打开浏览器"""
    try:
        print(f"🌐 正在打开浏览器配置文件: {profile_id}")
        
        client = IXBrowserClient()
        response = client.open_browser(profile_id)
        
        if response.get("code") == 0:
            data = response.get("data", {})
            driver_path = data.get("driver_path")
            debugger_address = data.get("debugger_address")
            
            print(f"✅ 浏览器启动成功")
            print(f"📍 Driver路径: {driver_path}")
            print(f"🔗 调试地址: {debugger_address}")
            
            # 连接到浏览器
            options = Options()
            options.add_experimental_option("debuggerAddress", debugger_address)
            driver = webdriver.Chrome(executable_path=driver_path, options=options)
            
            return driver
        else:
            error_message = response.get("message", "未知错误")
            print(f"❌ 浏览器启动失败: {error_message}")
            return None
            
    except Exception as e:
        print(f"❌ 打开浏览器时出错: {str(e)}")
        return None


def close_browser_by_profile_id(profile_id):
    """通过配置文件ID关闭浏览器"""
    try:
        print(f"🔒 正在关闭浏览器配置文件: {profile_id}")
        
        client = IXBrowserClient()
        response = client.close_browser(profile_id)
        
        if response.get("code") == 0:
            print(f"✅ 浏览器关闭成功")
            return True
        else:
            error_message = response.get("message", "未知错误")
            print(f"❌ 浏览器关闭失败: {error_message}")
            return False
            
    except Exception as e:
        print(f"❌ 关闭浏览器时出错: {str(e)}")
        return False


def check_zealy_login_status(driver):
    """检查 Zealy.io 登录状态"""
    try:
        print("🔍 检查 Zealy.io 登录状态...")

        # 等待页面加载
        time.sleep(3)

        # 检查登录状态的指标元素
        login_indicators = [
            # 用户头像或用户菜单
            "//img[contains(@class, 'avatar')]",
            "//div[contains(@class, 'avatar')]",
            "//button[contains(@class, 'avatar')]",
            # 用户名或用户信息
            "//div[contains(@class, 'user')]",
            "//span[contains(@class, 'username')]",
            "//div[contains(@class, 'profile')]",
            # 登录后的导航元素
            "//nav[contains(@class, 'user')]",
            "//div[contains(@class, 'user-menu')]",
            "//button[contains(@class, 'user-menu')]",
            # 钱包连接状态
            "//div[contains(@class, 'wallet-connected')]",
            "//button[contains(@class, 'wallet-connected')]",
            "//span[contains(text(), 'Connected')]",
            "//span[contains(text(), 'connected')]",
            # 断开连接按钮（说明已连接）
            "//button[contains(text(), 'Disconnect')]",
            "//button[contains(text(), 'disconnect')]",
            "//a[contains(text(), 'Disconnect')]",
            # 用户相关的下拉菜单
            "//div[@role='menu']",
            "//ul[contains(@class, 'dropdown')]",
            # 个人资料链接
            "//a[contains(@href, 'profile')]",
            "//a[contains(@href, 'account')]",
            "//a[contains(@href, 'settings')]"
        ]

        for indicator in login_indicators:
            try:
                elements = driver.find_elements(By.XPATH, indicator)
                for element in elements:
                    if element.is_displayed():
                        print(f"✅ 检测到登录指标: {indicator}")
                        return True
            except Exception as e:
                continue

        # 检查页面URL是否包含登录后的特征
        current_url = driver.current_url
        if any(keyword in current_url.lower() for keyword in ['dashboard', 'profile', 'account', 'user']):
            print(f"✅ URL显示已登录状态: {current_url}")
            return True

        print("❌ 未检测到登录状态")
        return False

    except Exception as e:
        print(f"❌ 检查登录状态时出错: {str(e)}")
        return False


def check_login_status_via_login_page(driver):
    """通过访问登录页面检测登录状态"""
    try:
        print("🔍 通过登录页面检测登录状态...")
        driver.get("https://zealy.io/login")

        # 等待页面加载
        time.sleep(5)

        # 如果已经登录，通常会被重定向到其他页面（如dashboard）
        current_url = driver.current_url.lower()

        # 检查是否被重定向到非登录页面
        if "/login" not in current_url:
            print(f"✅ 检测到已登录状态 - 被重定向到: {driver.current_url}")
            return True

        # 检查页面上是否有登录表单元素
        login_form_indicators = [
            "//input[@type='email']",
            "//input[@type='password']",
            "//button[contains(text(), 'Sign in')]",
            "//button[contains(text(), 'Login')]",
            "//button[contains(text(), 'Connect')]",
            "//div[contains(@class, 'login')]",
            "//form[contains(@class, 'login')]"
        ]

        has_login_form = False
        for indicator in login_form_indicators:
            try:
                elements = driver.find_elements(By.XPATH, indicator)
                if elements and any(elem.is_displayed() for elem in elements):
                    has_login_form = True
                    break
            except:
                continue

        if has_login_form:
            print("❌ 检测到登录表单，账户未登录")
            return False
        else:
            print("✅ 未找到登录表单，可能已登录")
            return True

    except Exception as e:
        print(f"❌ 检测登录状态时出错: {str(e)}")
        return False


def navigate_to_zealy(driver):
    """导航到 Zealy.io 网站"""
    try:
        print("🌐 正在检测 Zealy.io 登录状态...")

        # 首先检查登录状态
        if check_login_status_via_login_page(driver):
            print("✅ 检测到已登录状态，跳过登录流程")
            # 导航到目标页面
            print("🌐 导航到目标页面...")
            driver.get("https://zealy.io/cw/coingarage/questboard/")
            time.sleep(5)
            return "already_logged_in"

        print("❌ 检测到未登录状态，需要进行登录")
        print("🌐 正在访问 Zealy.io 主页...")
        driver.get("https://zealy.io/cw/coingarage/questboard/")

        # 等待页面加载
        time.sleep(5)

        # 检查页面是否正确加载
        if "zealy" in driver.current_url.lower():
            print("✅ 成功访问 Zealy.io，需要进行登录")
            return True
        else:
            print(f"❌ 页面加载异常，当前URL: {driver.current_url}")
            return False

    except Exception as e:
        print(f"❌ 访问 Zealy.io 时出错: {str(e)}")
        return False


def click_login_button(driver):
    """点击登录按钮"""
    try:
        print("🔍 查找并点击登录按钮...")
        
        # 可能的登录按钮选择器
        login_selectors = [
            "//button[contains(text(), 'Login')]",
            "//button[contains(text(), 'Sign in')]",
            "//button[contains(text(), 'Connect')]",
            "//a[contains(text(), 'Login')]",
            "//a[contains(text(), 'Sign in')]",
            "//a[contains(text(), 'Connect')]",
            "//button[contains(@class, 'login')]",
            "//button[contains(@class, 'signin')]",
            "//button[contains(@class, 'connect')]",
            "//div[contains(@class, 'login')]",
            "//div[contains(@class, 'signin')]",
            "//div[contains(@class, 'connect')]",
            "//span[contains(text(), 'Login')]/..",
            "//span[contains(text(), 'Sign in')]/..",
            "//span[contains(text(), 'Connect')]/.."
        ]
        
        for selector in login_selectors:
            try:
                print(f"🔍 尝试登录按钮选择器: {selector}")
                elements = driver.find_elements(By.XPATH, selector)
                
                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        element.click()
                        print(f"✅ 成功点击登录按钮")
                        time.sleep(3)
                        return True
                        
            except Exception as e:
                print(f"⚠️ 选择器失败: {selector} - {str(e)}")
                continue
        
        print("❌ 未找到可用的登录按钮")
        return False
        
    except Exception as e:
        print(f"❌ 点击登录按钮时出错: {str(e)}")
        return False


def select_okx_wallet_login(driver):
    """选择 OKX 钱包登录"""
    try:
        print("🔍 查找并选择 OKX 钱包登录选项...")
        
        # 等待登录选项加载
        time.sleep(3)
        
        # 可能的 OKX 钱包选择器
        okx_selectors = [
            "//button[contains(text(), 'OKX')]",
            "//div[contains(text(), 'OKX')]",
            "//span[contains(text(), 'OKX')]",
            "//img[contains(@alt, 'OKX')]/..",
            "//img[contains(@src, 'okx')]/..",
            "//button[contains(@class, 'okx')]",
            "//div[contains(@class, 'okx')]",
            "//a[contains(text(), 'OKX')]",
            "//button[contains(text(), 'okx')]",
            "//div[contains(text(), 'okx')]",
            "//span[contains(text(), 'okx')]"
        ]
        
        for selector in okx_selectors:
            try:
                print(f"🔍 尝试 OKX 选择器: {selector}")
                elements = driver.find_elements(By.XPATH, selector)
                
                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        element.click()
                        print(f"✅ 成功选择 OKX 钱包登录")
                        time.sleep(3)
                        return True
                        
            except Exception as e:
                print(f"⚠️ 选择器失败: {selector} - {str(e)}")
                continue
        
        print("❌ 未找到 OKX 钱包登录选项")
        return False
        
    except Exception as e:
        print(f"❌ 选择 OKX 钱包登录时出错: {str(e)}")
        return False


def handle_okx_authorization(driver):
    """处理 OKX 钱包授权"""
    try:
        print("🔐 处理 OKX 钱包授权...")
        
        # 等待 OKX 钱包弹窗出现
        time.sleep(5)
        
        # 记录当前窗口句柄
        main_window = driver.current_window_handle
        print(f"📋 主窗口句柄: {main_window}")
        
        # 等待新窗口出现
        max_wait_time = 30
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            all_windows = driver.window_handles
            print(f"📋 当前窗口数量: {len(all_windows)}")
            
            if len(all_windows) > 1:
                # 找到 OKX 钱包窗口
                for window_handle in all_windows:
                    if window_handle != main_window:
                        try:
                            driver.switch_to.window(window_handle)
                            window_title = driver.title
                            current_url = driver.current_url
                            
                            print(f"🔍 检查窗口: {window_title} - {current_url}")
                            
                            # 检查是否是 OKX 钱包窗口
                            if ("okx" in window_title.lower() or 
                                "okx" in current_url.lower() or
                                "wallet" in window_title.lower()):
                                
                                print(f"✅ 找到 OKX 钱包窗口: {window_title}")
                                
                                # 查找并点击授权按钮
                                if click_authorize_button(driver):
                                    print("✅ OKX 钱包授权成功")
                                    
                                    # 切换回主窗口
                                    driver.switch_to.window(main_window)
                                    time.sleep(3)
                                    return True
                                else:
                                    print("❌ 点击授权按钮失败")
                                    driver.switch_to.window(main_window)
                                    return False
                                    
                        except Exception as e:
                            print(f"⚠️ 检查窗口时出错: {str(e)}")
                            continue
            
            time.sleep(2)
        
        print("❌ 等待 OKX 钱包窗口超时")
        return False
        
    except Exception as e:
        print(f"❌ 处理 OKX 钱包授权时出错: {str(e)}")
        return False


def click_authorize_button(driver):
    """在 OKX 钱包窗口中点击授权按钮"""
    try:
        print("🔍 查找并点击授权按钮...")
        
        # 等待页面加载
        time.sleep(3)
        
        # 可能的授权按钮选择器
        authorize_selectors = [
            "//button[contains(text(), 'Authorize')]",
            "//button[contains(text(), 'Connect')]",
            "//button[contains(text(), 'Confirm')]",
            "//button[contains(text(), 'Allow')]",
            "//button[contains(text(), 'Approve')]",
            "//button[contains(text(), '授权')]",
            "//button[contains(text(), '连接')]",
            "//button[contains(text(), '确认')]",
            "//button[contains(text(), '同意')]",
            "//span[contains(text(), 'Authorize')]/..",
            "//span[contains(text(), 'Connect')]/..",
            "//span[contains(text(), 'Confirm')]/..",
            "//span[contains(text(), 'Allow')]/..",
            "//span[contains(text(), 'Approve')]/..",
            "//div[contains(@class, 'authorize')]",
            "//div[contains(@class, 'connect')]",
            "//div[contains(@class, 'confirm')]"
        ]
        
        for selector in authorize_selectors:
            try:
                print(f"🔍 尝试授权按钮选择器: {selector}")
                elements = driver.find_elements(By.XPATH, selector)
                
                for element in elements:
                    if element.is_displayed() and element.is_enabled():
                        element.click()
                        print(f"✅ 成功点击授权按钮")
                        time.sleep(3)
                        return True
                        
            except Exception as e:
                print(f"⚠️ 选择器失败: {selector} - {str(e)}")
                continue
        
        print("❌ 未找到可用的授权按钮")
        return False
        
    except Exception as e:
        print(f"❌ 点击授权按钮时出错: {str(e)}")
        return False


def process_zealy_login(profile_data):
    """处理单个账户的 Zealy 登录流程"""
    wallet_address = profile_data['wallet_address']
    profile_id = None
    driver = None
    
    try:
        print(f"\n{'='*60}")
        print(f"🚀 开始处理钱包地址: {wallet_address}")
        print(f"{'='*60}")
        
        # 1. 查找对应的浏览器配置文件
        profile_id = find_profile_by_wallet_address(wallet_address)
        if not profile_id:
            print(f"❌ 未找到钱包地址 {wallet_address} 对应的浏览器配置文件，跳过")
            return False
        
        # 2. 打开浏览器
        driver = open_browser_by_profile_id(profile_id)
        if not driver:
            print(f"❌ 浏览器打开失败，跳过该账户")
            return False
        
        # 3. 等待浏览器完全启动
        print("⏳ 等待浏览器完全启动...")
        time.sleep(5)

        # 4. 访问登录页面检测登录状态
        print("🌐 正在访问 Zealy.io 登录页面...")
        driver.get("https://zealy.io/login")

        # 等待页面完全加载
        print("⏳ 等待页面完全加载...")
        time.sleep(8)

        print(f"📍 当前URL: {driver.current_url}")
        print(f"📄 页面标题: {driver.title}")

        # 检测登录状态
        current_url = driver.current_url.lower()

        if "/login" not in current_url:
            print(f"✅ 检测到已登录状态 - 被重定向到: {driver.current_url}")
            print(f"✅ 钱包地址 {wallet_address} 已经登录 Zealy.io，跳过登录流程")
            return True

        print("❌ 检测到未登录状态，需要进行登录")

        # 5. 导航到目标页面进行登录
        print("🌐 正在访问 Zealy.io 主页...")
        driver.get("https://zealy.io/cw/coingarage/questboard/")
        time.sleep(5)

        # 6. 点击登录按钮（只有在未登录时才执行）
        if not click_login_button(driver):
            print(f"❌ 点击登录按钮失败")
            return False

        # 7. 选择 OKX 钱包登录
        if not select_okx_wallet_login(driver):
            print(f"❌ 选择 OKX 钱包登录失败")
            return False

        # 8. 处理 OKX 钱包授权
        if not handle_okx_authorization(driver):
            print(f"❌ OKX 钱包授权失败")
            return False

        print(f"✅ 钱包地址 {wallet_address} 登录 Zealy.io 成功！")
        
        # 等待一段时间确保登录完成
        time.sleep(5)
        
        return True
        
    except Exception as e:
        print(f"❌ 处理账户 {wallet_address} 时出错: {str(e)}")
        return False
        
    finally:
        # 清理资源
        try:
            if driver:
                driver.quit()
                print("🔒 浏览器已关闭")
        except:
            pass
            
        try:
            if profile_id:
                close_browser_by_profile_id(profile_id)
        except:
            pass


def main():
    """主函数"""
    try:
        print("🚀 开始 Zealy.io 钱包授权登录脚本")
        print("=" * 60)
        
        # 读取账户数据文件
        data_file = input("请输入数据文件路径 (默认: accounts.txt): ").strip()
        if not data_file:
            data_file = "accounts.txt"
        
        if not os.path.exists(data_file):
            print(f"❌ 数据文件 {data_file} 不存在")
            return
        
        # 读取账户数据
        profiles_data = read_data_file(data_file)
        print(f"📋 读取到 {len(profiles_data)} 个账户")
        
        if not profiles_data:
            print("❌ 没有读取到有效的账户数据")
            return
        
        # 处理每个账户
        success_count = 0
        failed_count = 0
        
        for i, profile_data in enumerate(profiles_data, 1):
            try:
                print(f"\n🔄 处理第 {i}/{len(profiles_data)} 个账户")
                
                # 切换IP地址
                if i > 1:  # 第一个账户不需要切换IP
                    print("🔄 切换IP地址...")
                    switch_ip()
                    time.sleep(60)  # 等待60秒
                
                # 处理登录
                if process_zealy_login(profile_data):
                    success_count += 1
                    print(f"✅ 第 {i} 个账户处理成功")
                else:
                    failed_count += 1
                    print(f"❌ 第 {i} 个账户处理失败")
                    # 将失败的账户保存到文件
                    handle_failed_profile(profile_data, 'failed_zealy_login.txt')
                
                # 账户间等待30秒
                if i < len(profiles_data):
                    print("⏳ 等待30秒后处理下一个账户...")
                    time.sleep(30)
                
            except Exception as e:
                failed_count += 1
                print(f"❌ 处理第 {i} 个账户时出错: {str(e)}")
                handle_failed_profile(profile_data, 'failed_zealy_login.txt')
                continue
        
        # 输出统计结果
        print(f"\n{'='*60}")
        print(f"📊 处理完成统计:")
        print(f"✅ 成功: {success_count} 个账户")
        print(f"❌ 失败: {failed_count} 个账户")
        print(f"📋 总计: {len(profiles_data)} 个账户")
        print(f"{'='*60}")
        
        if failed_count > 0:
            print(f"💾 失败的账户已保存到 failed_zealy_login.txt")
        
    except Exception as e:
        print(f"❌ 主程序执行时出错: {str(e)}")
        import traceback
        print(f"🔍 详细错误信息: {traceback.format_exc()}")


if __name__ == "__main__":
    main()
