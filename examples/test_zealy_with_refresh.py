import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options

from ixbrowser_local_api import IXBrowserClient
from batch_browser_zealy import (
    get_profile_list, 
    wait_for_page_load,
    click_join_button,
    log_zealy_result,
    ensure_okx_button_loaded,
    check_element_exists
)


def test_zealy_with_refresh():
    """测试带刷新功能的Zealy + OKX钱包登录流程"""
    print("🧪 测试带刷新功能的Zealy + OKX钱包登录流程")
    print("=" * 60)
    
    # 测试钱包地址
    test_wallet = "******************************************"
    
    # 初始化客户端
    client = IXBrowserClient()
    
    # 1. 查找配置文件
    print(f"🔍 查找配置文件: {test_wallet}")
    profiles, error_code, error_message = get_profile_list(test_wallet)
    
    if not profiles:
        print(f"❌ 未找到配置文件: {test_wallet}")
        if error_message:
            print(f"   错误信息: {error_message}")
        return
    
    profile_id = profiles[0].get('profile_id')
    profile_name = profiles[0].get('name', 'Unknown')
    print(f"✅ 找到配置文件:")
    print(f"   ID: {profile_id}")
    print(f"   名称: {profile_name}")
    
    # 2. 打开浏览器
    print(f"\n🚀 正在打开浏览器...")
    open_result = client.open_profile(profile_id, load_profile_info_page=False)
    
    if not open_result:
        print(f"❌ 打开浏览器失败: {client.code} - {client.message}")
        return
    
    debug_address = open_result.get('debugging_address', '')
    print(f"🔗 调试地址: {debug_address}")
    
    # 3. 连接Selenium
    if debug_address.startswith('http://'):
        debug_address = debug_address[7:]
    elif debug_address.startswith('https://'):
        debug_address = debug_address[8:]
    
    chrome_options = Options()
    chrome_options.debugger_address = debug_address
    
    driver = None
    try:
        # 初始化WebDriver
        webdriver_path = open_result.get('webdriver', '')
        if webdriver_path:
            service = webdriver.chrome.service.Service(executable_path=webdriver_path)
            driver = webdriver.Chrome(service=service, options=chrome_options)
        else:
            driver = webdriver.Chrome(options=chrome_options)
        
        print("✅ WebDriver连接成功")
        
        # 4. 访问Zealy页面
        zealy_url = "https://zealy.io/cw/coingarage/questboard/"
        print(f"\n🌐 正在访问Zealy页面...")
        print(f"📍 URL: {zealy_url}")
        
        try:
            driver.get(zealy_url)
            print("✅ 页面访问成功")
        except Exception as e:
            print(f"❌ 页面访问失败: {str(e)}")
            return
        
        # 5. 等待页面加载
        print("⏳ 等待页面加载...")
        if wait_for_page_load(driver):
            print("✅ 页面加载完成")
        else:
            print("⚠️ 页面加载可能不完整，但继续测试")
        
        # 6. 测试OKX按钮检测功能
        print(f"\n🔍 测试OKX按钮检测功能")
        print("=" * 30)
        
        # 先测试按钮检测
        okx_button_xpath = "/html/body/div[2]/div/div/div[2]/div/div/div/div/div[1]/div[2]/div[2]/div/button/div/div/div[2]/div"
        
        print("🔍 检查OKX按钮是否存在...")
        if check_element_exists(driver, okx_button_xpath):
            print("✅ OKX按钮已存在")
        else:
            print("❌ OKX按钮不存在，需要通过登录流程加载")
        
        # 7. 执行完整的登录流程（包含智能刷新）
        print(f"\n🎯 开始执行带智能刷新的登录流程")
        print("=" * 50)
        
        login_success = click_join_button(driver)
        
        if login_success:
            print("✅ 带智能刷新的登录流程测试成功！")
            log_zealy_result(test_wallet, True)
            
            # 检查登录结果
            print(f"\n🔍 检查登录结果:")
            current_url = driver.current_url
            page_title = driver.title
            
            print(f"📍 当前URL: {current_url}")
            print(f"📄 页面标题: {page_title}")
            
            # 检查是否有登录成功的标志
            success_indicators = [
                "dashboard", "profile", "quest", "task", "reward"
            ]
            
            found_indicators = []
            for indicator in success_indicators:
                if indicator.lower() in current_url.lower() or indicator.lower() in page_title.lower():
                    found_indicators.append(indicator)
            
            if found_indicators:
                print(f"✅ 检测到登录成功标志: {', '.join(found_indicators)}")
            else:
                print("⚠️ 未检测到明确的登录成功标志")
                
        else:
            print("❌ 带智能刷新的登录流程测试失败")
            log_zealy_result(test_wallet, False, "智能刷新登录流程失败")
        
        # 8. 保持浏览器打开供检查
        print(f"\n💡 浏览器将保持打开状态供检查结果")
        print("💡 请检查:")
        print("   1. 是否成功登录到Zealy")
        print("   2. 是否显示了用户信息")
        print("   3. 是否可以看到任务列表")
        print("   4. OKX钱包是否正确连接")
        
        input("\n按Enter键关闭浏览器...")
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {str(e)}")
        log_zealy_result(test_wallet, False, f"测试出错: {str(e)}")
    finally:
        # 关闭浏览器
        if driver:
            try:
                print("🔒 正在关闭浏览器...")
                close_result = client.close_profile(profile_id)
                if close_result:
                    print("✅ 浏览器已关闭")
                else:
                    print(f"⚠️ API关闭失败，使用Selenium关闭")
                    driver.quit()
            except Exception as e:
                print(f"⚠️ 关闭浏览器时出错: {str(e)}")
                try:
                    driver.quit()
                except:
                    pass


def show_improved_features():
    """显示改进的功能特点"""
    print("💡 改进的Zealy登录功能")
    print("=" * 40)
    print("🔧 新增功能:")
    print("   1. 🔍 智能检测OKX按钮是否存在")
    print("   2. 🔄 自动刷新页面重新加载按钮")
    print("   3. 🔁 最多重试3次确保按钮加载")
    print("   4. 📸 失败时自动截图保存")
    print("   5. ⏱️ 优化的等待时间")
    print()
    print("🔄 智能刷新流程:")
    print("   1. 检查OKX按钮是否存在")
    print("   2. 如果不存在，刷新页面")
    print("   3. 重新执行登录流程")
    print("   4. 再次检查按钮")
    print("   5. 重复最多3次")
    print()
    print("⚡ 优势:")
    print("   - 解决页面加载不完整问题")
    print("   - 提高登录成功率")
    print("   - 自动处理网络延迟")
    print("   - 减少手动干预需求")


if __name__ == '__main__':
    print("🚀 开始测试带智能刷新的Zealy登录功能")
    print("=" * 70)
    
    try:
        # 显示改进功能
        show_improved_features()
        
        # 询问是否运行测试
        choice = input("\n是否运行实际登录测试? (y/n): ").strip().lower()
        if choice == 'y':
            test_zealy_with_refresh()
        else:
            print("跳过实际登录测试")
        
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {str(e)}")
