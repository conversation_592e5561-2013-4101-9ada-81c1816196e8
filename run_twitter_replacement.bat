@echo off
echo ========================================
echo Twitter Token 替换自动化脚本
echo ========================================
echo.

REM 检查必要文件
if not exist "accounts.txt" (
    echo ❌ 错误: 找不到 accounts.txt 文件
    echo 请确保账户文件存在并格式正确
    pause
    exit /b 1
)

if not exist "twitter_tokens.txt" (
    echo ❌ 错误: 找不到 twitter_tokens.txt 文件
    echo 请创建该文件并添加有效的Twitter tokens
    pause
    exit /b 1
)

echo ✅ 文件检查完成
echo.

REM 显示当前状态
echo 📊 当前状态:
python examples/monitor_progress.py status
echo.

REM 询问用户是否继续
set /p choice="是否开始执行脚本? (y/n): "
if /i "%choice%" neq "y" (
    echo 操作已取消
    pause
    exit /b 0
)

echo.
echo 🚀 启动Twitter Token替换脚本...
echo 💡 提示: 可以在另一个终端运行 'python examples/monitor_progress.py' 来监控进度
echo.

REM 运行主脚本
python examples/twitter_token_replacement.py

echo.
echo 📊 执行完成，显示最终状态:
python examples/monitor_progress.py status

pause
