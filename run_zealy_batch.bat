@echo off
echo ========================================
echo Zealy批量浏览器打开脚本
echo ========================================
echo.

REM 检查必要文件
if not exist "accounts.txt" (
    echo ❌ 错误: 找不到 accounts.txt 文件
    echo 请确保账户文件存在并格式正确
    pause
    exit /b 1
)

echo ✅ 文件检查完成
echo.

REM 显示当前状态
echo 📊 当前状态:
python -c "
import os
from auto_profile_setup import read_data_file
try:
    accounts_data = read_data_file('accounts.txt')
    print(f'📋 账户文件: accounts.txt')
    print(f'   - 总账户数: {len(accounts_data)}')
    print(f'   - 文件大小: {os.path.getsize(\"accounts.txt\")} 字节')
    print('🎯 目标页面: https://zealy.io/cw/coingarage/questboard/')
    print('💡 注意: 浏览器将保持打开状态供您继续操作')
except Exception as e:
    print(f'❌ 读取账户文件失败: {str(e)}')
"
echo.

REM 询问用户是否继续
set /p choice="是否开始执行Zealy批量浏览器脚本? (y/n): "
if /i "%choice%" neq "y" (
    echo 操作已取消
    pause
    exit /b 0
)

echo.
echo 🚀 启动Zealy批量浏览器脚本...
echo 💡 提示: 脚本会自动打开浏览器并访问Zealy页面
echo 💡 浏览器将保持打开状态供您继续操作
echo.

REM 运行主脚本
python examples/batch_browser_zealy.py

echo.
echo 📊 执行完成，显示最终状态:
python -c "
import os
try:
    if os.path.exists('zealy_results.log'):
        with open('zealy_results.log', 'r', encoding='utf-8') as f:
            lines = f.readlines()
            print(f'📝 处理日志: zealy_results.log ({len(lines)} 条记录)')
            if lines:
                print('   最近的记录:')
                for line in lines[-3:]:
                    print(f'   {line.strip()}')
    else:
        print('📝 没有生成处理日志')
        
    if os.path.exists('failed_zealy.txt'):
        with open('failed_zealy.txt', 'r', encoding='utf-8') as f:
            failed_lines = f.readlines()
            print(f'❌ 失败记录: failed_zealy.txt ({len(failed_lines)} 条记录)')
    else:
        print('✅ 没有失败记录')
        
    print('💡 成功打开的浏览器仍在运行，请手动完成后续操作')
except Exception as e:
    print(f'❌ 读取日志文件失败: {str(e)}')
"

pause
